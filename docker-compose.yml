services:
  web:
    image: ghcr.io/cedar2025/xboard:new
    volumes:
      # 映射配置文件、日志、主题和插件目录
      - ./.env:/www/.env
      - ./.docker/.data/:/www/.docker/.data # 保留这个通用数据映射
      - ./storage/logs:/www/storage/logs
      - ./storage/theme:/www/storage/theme
      - ./plugins:/www/plugins
    environment:
      - docker=true
    # 移除了 depends_on: - redis
    # 移除了 network_mode: host
    command: php artisan octane:start --port=7001 --host=0.0.0.0
    restart: always # 或者 on-failure，根据需要选择
    ports: # 添加端口映射，供反向代理访问
      - "7001:7001"
    networks: # 连接到 1Panel 网络
      - 1panel-network

  horizon:
    image: ghcr.io/cedar2025/xboard:new
    volumes:
      # 映射配置文件、日志和插件目录
      - ./.env:/www/.env
      - ./.docker/.data/:/www/.docker/.data # 保留这个通用数据映射
      - ./storage/logs:/www/storage/logs
      - ./plugins:/www/plugins
    restart: always # 或者 on-failure
    # 移除了 network_mode: host
    command: php artisan horizon
    # 移除了 depends_on: - redis
    networks: # 连接到 1Panel 网络
      - 1panel-network

# 移除了 redis 服务块

networks: # 定义外部网络
  1panel-network:
    external: true
