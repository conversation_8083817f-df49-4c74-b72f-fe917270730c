import{r as m,j as e,t as Sl,c as kl,I as Ka,a as Ls,S as _a,u as ks,b as wa,R as fn,d as gn,e as Tl,F as Dl,C as Pl,L as jn,T as vn,f as bn,g as El,h as Rl,i as Il,k as A,z as h,l as M,m as fe,n as ye,o as ie,p as Ze,q as Ce,s as it,v as Vl,O as Ca,w as Ml,x as Fl,y as Ol,A as zl,B as Ll,D as Al,Q as $l,E as ql,G as Hl,H as Ul,P as Kl,J as Bl,K as Gl,M as Wl,N as Yl,U as yn,V as Nn,W as $t,X as qt,Y as Sa,Z as Xe,_ as Ht,$ as Ut,a0 as _n,a1 as wn,a2 as Cn,a3 as ka,a4 as Sn,a5 as Ql,a6 as kn,a7 as Tn,a8 as Dn,a9 as Pn,aa as As,ab as En,ac as Jl,ad as Rn,ae as In,af as Zl,ag as Xl,ah as ei,ai as si,aj as ti,ak as ai,al as ni,am as ri,an as li,ao as ii,ap as oi,aq as Vn,ar as ci,as as di,at as $s,au as Mn,av as ui,aw as mi,ax as Fn,ay as Ta,az as xi,aA as hi,aB as Ba,aC as pi,aD as On,aE as fi,aF as zn,aG as gi,aH as ji,aI as vi,aJ as bi,aK as yi,aL as Ni,aM as Ln,aN as _i,aO as wi,aP as Ci,aQ as Fe,aR as Si,aS as An,aT as $n,aU as ki,aV as Ti,aW as qn,aX as Di,aY as Pi,aZ as Je,a_ as Ei,a$ as Ri,b0 as Hn,b1 as Un,b2 as Kn,b3 as Ii,b4 as Vi,b5 as Mi,b6 as Bn,b7 as Fi,b8 as Da,b9 as Gn,ba as Oi,bb as Wn,bc as zi,bd as Yn,be as Li,bf as Qn,bg as Jn,bh as Ai,bi as $i,bj as Zn,bk as qi,bl as Hi,bm as Xn,bn as Ui,bo as er,bp as Ki,bq as Bi,br as ls,bs as jt,bt as Gi,bu as Wi,bv as Yi,bw as Qi,bx as Ji,by as Zi,bz as Ga,bA as Wa,bB as Xi,bC as eo,bD as so,bE as to,bF as ao,bG as pa,bH as pt,bI as no,bJ as ro,bK as lo,bL as Ya,bM as fa,bN as ga,bO as io,bP as oo,bQ as sr,bR as co,bS as Pa,bT as uo,bU as mo,bV as xo,bW as tr,bX as ar,bY as ho,bZ as po,b_ as fo,b$ as go,c0 as jo,c1 as nr,c2 as vo,c3 as bo,c4 as yo,c5 as No,c6 as Vt,c7 as Re,c8 as Qa,c9 as _o,ca as rr,cb as lr,cc as ir,cd as or,ce as cr,cf as dr,cg as wo,ch as Co,ci as So,cj as Kt,ck as qs,cl as os,cm as ts,cn as as,co as cs,cp as ds,cq as us,cr as ko,cs as To,ct as Do,cu as Po,cv as Eo,cw as Ro,cx as Io,cy as Vo,cz as Mo,cA as ja,cB as Ea,cC as Ra,cD as Fo,cE as Ts,cF as Ds,cG as vt,cH as Oo,cI as Mt,cJ as zo,cK as Ja,cL as ur,cM as Za,cN as Ft,cO as Lo,cP as Ao,cQ as $o,cR as qo,cS as mr,cT as Ho,cU as Uo,cV as xr,cW as va,cX as hr,cY as Ko,cZ as pr,c_ as fr,c$ as Bo,d0 as Go,d1 as Wo,d2 as Yo,d3 as Qo}from"./vendor.js";import"./index.js";var cp=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function dp(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}function Jo(s){if(s.__esModule)return s;var n=s.default;if(typeof n=="function"){var a=function r(){return this instanceof r?Reflect.construct(n,arguments,this.constructor):n.apply(this,arguments)};a.prototype=n.prototype}else a={};return Object.defineProperty(a,"__esModule",{value:!0}),Object.keys(s).forEach(function(r){var l=Object.getOwnPropertyDescriptor(s,r);Object.defineProperty(a,r,l.get?l:{enumerable:!0,get:function(){return s[r]}})}),a}const Zo={theme:"system",setTheme:()=>null},gr=m.createContext(Zo);function Xo({children:s,defaultTheme:n="system",storageKey:a="vite-ui-theme",...r}){const[l,c]=m.useState(()=>localStorage.getItem(a)||n);m.useEffect(()=>{const u=window.document.documentElement;if(u.classList.remove("light","dark"),l==="system"){const x=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";u.classList.add(x);return}u.classList.add(l)},[l]);const o={theme:l,setTheme:u=>{localStorage.setItem(a,u),c(u)}};return e.jsx(gr.Provider,{...r,value:o,children:s})}const ec=()=>{const s=m.useContext(gr);if(s===void 0)throw new Error("useTheme must be used within a ThemeProvider");return s},sc=function(){const n=typeof document<"u"&&document.createElement("link").relList;return n&&n.supports&&n.supports("modulepreload")?"modulepreload":"preload"}(),tc=function(s,n){return new URL(s,n).href},Xa={},ue=function(n,a,r){let l=Promise.resolve();if(a&&a.length>0){const o=document.getElementsByTagName("link"),u=document.querySelector("meta[property=csp-nonce]"),x=u?.nonce||u?.getAttribute("nonce");l=Promise.allSettled(a.map(i=>{if(i=tc(i,r),i in Xa)return;Xa[i]=!0;const d=i.endsWith(".css"),f=d?'[rel="stylesheet"]':"";if(!!r)for(let C=o.length-1;C>=0;C--){const p=o[C];if(p.href===i&&(!d||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${f}`))return;const P=document.createElement("link");if(P.rel=d?"stylesheet":sc,d||(P.as="script"),P.crossOrigin="",P.href=i,x&&P.setAttribute("nonce",x),document.head.appendChild(P),d)return new Promise((C,p)=>{P.addEventListener("load",C),P.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${i}`)))})}))}function c(o){const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=o,window.dispatchEvent(u),!u.defaultPrevented)throw o}return l.then(o=>{for(const u of o||[])u.status==="rejected"&&c(u.reason);return n().catch(c)})};function _(...s){return Sl(kl(s))}const tt=Ls("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),D=m.forwardRef(({className:s,variant:n,size:a,asChild:r=!1,children:l,disabled:c,loading:o=!1,leftSection:u,rightSection:x,...i},d)=>{const f=r?_a:"button";return e.jsxs(f,{className:_(tt({variant:n,size:a,className:s})),disabled:o||c,ref:d,...i,children:[(u&&o||!u&&!x&&o)&&e.jsx(Ka,{className:"mr-2 h-4 w-4 animate-spin"}),!o&&u&&e.jsx("div",{className:"mr-2",children:u}),l,!o&&x&&e.jsx("div",{className:"ml-2",children:x}),x&&o&&e.jsx(Ka,{className:"ml-2 h-4 w-4 animate-spin"})]})});D.displayName="Button";function Gs({className:s,minimal:n=!1}){const a=ks();return e.jsx("div",{className:_("h-svh w-full",s),children:e.jsxs("div",{className:"m-auto flex h-full w-full flex-col items-center justify-center gap-2",children:[!n&&e.jsx("h1",{className:"text-[7rem] font-bold leading-tight",children:"500"}),e.jsxs("span",{className:"font-medium",children:["Oops! Something went wrong ",":')"]}),e.jsxs("p",{className:"text-center text-muted-foreground",children:["We apologize for the inconvenience. ",e.jsx("br",{})," Please try again later."]}),!n&&e.jsxs("div",{className:"mt-6 flex gap-4",children:[e.jsx(D,{variant:"outline",onClick:()=>a(-1),children:"Go Back"}),e.jsx(D,{onClick:()=>a("/"),children:"Back to Home"})]})]})})}function en(){const s=ks();return e.jsx("div",{className:"h-svh",children:e.jsxs("div",{className:"m-auto flex h-full w-full flex-col items-center justify-center gap-2",children:[e.jsx("h1",{className:"text-[7rem] font-bold leading-tight",children:"404"}),e.jsx("span",{className:"font-medium",children:"Oops! Page Not Found!"}),e.jsxs("p",{className:"text-center text-muted-foreground",children:["It seems like the page you're looking for ",e.jsx("br",{}),"does not exist or might have been removed."]}),e.jsxs("div",{className:"mt-6 flex gap-4",children:[e.jsx(D,{variant:"outline",onClick:()=>s(-1),children:"Go Back"}),e.jsx(D,{onClick:()=>s("/"),children:"Back to Home"})]})]})})}function ac(){return e.jsx("div",{className:"h-svh",children:e.jsxs("div",{className:"m-auto flex h-full w-full flex-col items-center justify-center gap-2",children:[e.jsx("h1",{className:"text-[7rem] font-bold leading-tight",children:"503"}),e.jsx("span",{className:"font-medium",children:"Website is under maintenance!"}),e.jsxs("p",{className:"text-center text-muted-foreground",children:["The site is not available at the moment. ",e.jsx("br",{}),"We'll be back online shortly."]}),e.jsx("div",{className:"mt-6 flex gap-4",children:e.jsx(D,{variant:"outline",children:"Learn more"})})]})})}function nc(s){return typeof s>"u"}function rc(s){return s===null}function lc(s){return rc(s)||nc(s)}class ic{storage;prefixKey;constructor(n){this.storage=n.storage,this.prefixKey=n.prefixKey}getKey(n){return`${this.prefixKey}${n}`.toUpperCase()}set(n,a,r=null){const l=JSON.stringify({value:a,time:Date.now(),expire:r!==null?new Date().getTime()+r*1e3:null});this.storage.setItem(this.getKey(n),l)}get(n,a=null){const r=this.storage.getItem(this.getKey(n));if(!r)return{value:a,time:0};try{const l=JSON.parse(r),{value:c,time:o,expire:u}=l;return lc(u)||u>new Date().getTime()?{value:c,time:o}:(this.remove(n),{value:a,time:0})}catch{return this.remove(n),{value:a,time:0}}}remove(n){this.storage.removeItem(this.getKey(n))}clear(){this.storage.clear()}}function jr({prefixKey:s="",storage:n=sessionStorage}){return new ic({prefixKey:s,storage:n})}const vr="Xboard_",oc=function(s={}){return jr({prefixKey:s.prefixKey||"",storage:localStorage})},cc=function(s={}){return jr({prefixKey:s.prefixKey||"",storage:sessionStorage})},Bt=oc({prefixKey:vr});cc({prefixKey:vr});const br="access_token";function ft(){return Bt.get(br)}function yr(){Bt.remove(br)}const sn=["/sign-in","/sign-in-2","/sign-up","/forgot-password","/otp"];function dc({children:s}){const n=ks(),a=wa(),r=ft();return m.useEffect(()=>{if(!r.value&&!sn.includes(a.pathname)){const l=encodeURIComponent(a.pathname+a.search);n(`/sign-in?redirect=${l}`)}},[r.value,a.pathname,a.search,n]),sn.includes(a.pathname)||r.value?e.jsx(e.Fragment,{children:s}):null}const Se=m.forwardRef(({className:s,orientation:n="horizontal",decorative:a=!0,...r},l)=>e.jsx(fn,{ref:l,decorative:a,orientation:n,className:_("shrink-0 bg-border",n==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",s),...r}));Se.displayName=fn.displayName;const uc=Ls("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Ot=m.forwardRef(({className:s,...n},a)=>e.jsx(gn,{ref:a,className:_(uc(),s),...n}));Ot.displayName=gn.displayName;const Ne=Dl,Nr=m.createContext({}),b=({...s})=>e.jsx(Nr.Provider,{value:{name:s.name},children:e.jsx(Pl,{...s})}),Gt=()=>{const s=m.useContext(Nr),n=m.useContext(_r),{getFieldState:a,formState:r}=Tl(),l=a(s.name,r);if(!s)throw new Error("useFormField should be used within <FormField>");const{id:c}=n;return{id:c,name:s.name,formItemId:`${c}-form-item`,formDescriptionId:`${c}-form-item-description`,formMessageId:`${c}-form-item-message`,...l}},_r=m.createContext({}),v=m.forwardRef(({className:s,...n},a)=>{const r=m.useId();return e.jsx(_r.Provider,{value:{id:r},children:e.jsx("div",{ref:a,className:_("space-y-2",s),...n})})});v.displayName="FormItem";const y=m.forwardRef(({className:s,...n},a)=>{const{error:r,formItemId:l}=Gt();return e.jsx(Ot,{ref:a,className:_(r&&"text-destructive",s),htmlFor:l,...n})});y.displayName="FormLabel";const N=m.forwardRef(({...s},n)=>{const{error:a,formItemId:r,formDescriptionId:l,formMessageId:c}=Gt();return e.jsx(_a,{ref:n,id:r,"aria-describedby":a?`${l} ${c}`:`${l}`,"aria-invalid":!!a,...s})});N.displayName="FormControl";const z=m.forwardRef(({className:s,...n},a)=>{const{formDescriptionId:r}=Gt();return e.jsx("p",{ref:a,id:r,className:_("text-[0.8rem] text-muted-foreground",s),...n})});z.displayName="FormDescription";const k=m.forwardRef(({className:s,children:n,...a},r)=>{const{error:l,formMessageId:c}=Gt(),o=l?String(l?.message):n;return o?e.jsx("p",{ref:r,id:c,className:_("text-[0.8rem] font-medium text-destructive",s),...a,children:o}):null});k.displayName="FormMessage";const Wt=El,bt=m.forwardRef(({className:s,...n},a)=>e.jsx(jn,{ref:a,className:_("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",s),...n}));bt.displayName=jn.displayName;const Ke=m.forwardRef(({className:s,...n},a)=>e.jsx(vn,{ref:a,className:_("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",s),...n}));Ke.displayName=vn.displayName;const Ns=m.forwardRef(({className:s,...n},a)=>e.jsx(bn,{ref:a,className:_("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...n}));Ns.displayName=bn.displayName;function _e(s=void 0,n="YYYY-MM-DD HH:mm:ss"){return s==null?"":(Math.floor(s).toString().length===10&&(s=s*1e3),Rl(s).format(n))}function mc(s=void 0,n="YYYY-MM-DD"){return _e(s,n)}function Qs(s){const n=typeof s=="string"?parseFloat(s):s;return isNaN(n)?"0.00":n.toFixed(2)}function Fs(s,n=!0){if(s==null)return n?"¥0.00":"0.00";const a=typeof s=="string"?parseFloat(s):s;if(isNaN(a))return n?"¥0.00":"0.00";const l=(a/100).toFixed(2).replace(/\.?0+$/,c=>c.includes(".")?".00":c);return n?`¥${l}`:l}function zt(s){return new Promise(n=>{(async()=>{try{if(navigator.clipboard)await navigator.clipboard.writeText(s);else{const r=document.createElement("textarea");r.value=s,r.style.position="fixed",r.style.opacity="0",document.body.appendChild(r),r.select();const l=document.execCommand("copy");if(document.body.removeChild(r),!l)throw new Error("execCommand failed")}n(!0)}catch(r){console.error(r),n(!1)}})()})}function hs(s){const n=s/1024,a=n/1024,r=a/1024,l=r/1024;return l>=1?Qs(l)+" TB":r>=1?Qs(r)+" GB":a>=1?Qs(a)+" MB":Qs(n)+" KB"}const xc="locale";function hc(){return Bt.get(xc)}function wr(){yr();const s=window.location.pathname,n=s&&!["/404","/sign-in"].includes(s),a=new URL(window.location.href),l=`${a.pathname.split("/")[1]?`/${a.pathname.split("/")[1]}`:""}#/sign-in`;window.location.href=l+(n?`?redirect=${s}`:"")}const pc=["/passport/auth/login","/passport/auth/token2Login","/passport/auth/register","/guest/comm/config","/passport/comm/sendEmailVerify","/passport/auth/forget"];function fc(){const s=window.settings?.base_url||"/";return s.endsWith("/")?s+"api/v2":s+"/api/v2"}const V=Il.create({baseURL:fc(),timeout:12e3,headers:{"Content-Type":"application/json"}});V.interceptors.request.use(s=>{s.method?.toLowerCase()==="get"&&(s.params={...s.params,t:Date.now()});const n=ft();if(!pc.includes(s.url?.split("?")[0]||"")){if(!n.value)return wr(),Promise.reject({code:-1,message:"未登录"});s.headers.Authorization=n.value}return s.headers["Content-Language"]=hc().value||"zh-CN",s},s=>Promise.reject(s));V.interceptors.response.use(s=>s?.data||{code:-1,message:"未知错误"},s=>{const n=s.response?.status,a=s.response?.data?.message;return(n===401||n===403)&&wr(),A.error(a||{401:"登录已过期",403:"没有权限",404:"资源或接口不存在"}[n]||"未知异常"),Promise.reject(s.response?.data||{data:null,code:-1,message:"未知错误"})});const gc="access_token";function jc(s){Bt.set(gc,s)}const U=window?.settings?.secure_path,vc=s=>V.get(U+"/stat/getOrder",{params:s}),bc=()=>V.get(U+"/stat/getStats"),tn=s=>V.get(U+"/stat/getTrafficRank",{params:s}),yc=()=>V.get(U+"/theme/getThemes"),Nc=s=>V.post(U+"/theme/getThemeConfig",{name:s}),_c=(s,n)=>V.post(U+"/theme/saveThemeConfig",{name:s,config:n}),wc=s=>{const n=new FormData;return n.append("file",s),V.post(U+"/theme/upload",n,{headers:{"Content-Type":"multipart/form-data"}})},Cc=s=>V.post(U+"/theme/delete",{name:s}),Sc=s=>V.post(U+"/config/save",s),Cr=()=>V.get(U+"/server/manage/getNodes"),kc=s=>V.post(U+"/server/manage/save",s),Tc=s=>V.post(U+"/server/manage/drop",s),Dc=s=>V.post(U+"/server/manage/copy",s),Pc=s=>V.post(U+"/server/manage/update",s),Ec=s=>V.post(U+"/server/manage/sort",s),yt=()=>V.get(U+"/server/group/fetch"),Rc=s=>V.post(U+"/server/group/save",s),Ic=s=>V.post(U+"/server/group/drop",s),Sr=()=>V.get(U+"/server/route/fetch"),Vc=s=>V.post(U+"/server/route/save",s),Mc=s=>V.post(U+"/server/route/drop",s),Fc=()=>V.get(U+"/payment/fetch"),Oc=()=>V.get(U+"/payment/getPaymentMethods"),zc=s=>V.post(U+"/payment/getPaymentForm",s),Lc=s=>V.post(U+"/payment/save",s),Ac=s=>V.post(U+"/payment/drop",s),$c=s=>V.post(U+"/payment/show",s),qc=s=>V.post(U+"/payment/sort",s),Hc=s=>V.post(U+"/notice/save",s),Uc=s=>V.post(U+"/notice/drop",s),Kc=s=>V.post(U+"/notice/show",s),Bc=()=>V.get(U+"/knowledge/fetch"),Gc=s=>V.get(U+"/knowledge/fetch?id="+s),Wc=s=>V.post(U+"/knowledge/save",s),Yc=s=>V.post(U+"/knowledge/drop",s),Qc=s=>V.post(U+"/knowledge/show",s),Jc=s=>V.post(U+"/knowledge/sort",s),Hs=()=>V.get(U+"/plan/fetch"),Zc=s=>V.post(U+"/plan/save",s),na=s=>V.post(U+"/plan/update",s),Xc=s=>V.post(U+"/plan/drop",s),ed=s=>V.post(U+"/plan/sort",{ids:s}),sd=async s=>V.post(U+"/order/fetch",s),td=s=>V.post(U+"/order/detail",s),ad=s=>V.post(U+"/order/paid",s),nd=s=>V.post(U+"/order/cancel",s),an=s=>V.post(U+"/order/update",s),rd=s=>V.post(U+"/order/assign",s),ld=s=>V.post(U+"/coupon/fetch",s),id=s=>V.post(U+"/coupon/generate",s),od=s=>V.post(U+"/coupon/drop",s),cd=s=>V.post(U+"/coupon/update",s),dd=s=>V.post(U+"/user/fetch",s),ud=s=>V.post(U+"/user/update",s),md=s=>V.post(U+"/user/resetSecret",s),xd=s=>V.post(U+"/user/generate",s),hd=s=>V.post(U+"/stat/getStatUser",s),pd=s=>V.post(U+"/ticket/fetch",s),fd=s=>V.get(U+"/ticket/fetch?id= "+s),gd=s=>V.post(U+"/ticket/close",{id:s}),fs=(s="")=>V.get(U+"/config/fetch?key="+s),gs=s=>V.post(U+"/config/save",s),jd=()=>V.get(U+"/config/getEmailTemplate"),vd=()=>V.post(U+"/config/testSendMail"),bd=()=>V.post(U+"/config/setTelegramWebhook"),yd=h.object({subscribe_template_singbox:h.string().nullable(),subscribe_template_clash:h.string().nullable(),subscribe_template_clashmeta:h.string().nullable(),subscribe_template_stash:h.string().nullable(),subscribe_template_surge:h.string().nullable()}),Nd={subscribe_template_singbox:"",subscribe_template_clash:"",subscribe_template_clashmeta:"",subscribe_template_stash:"",subscribe_template_surge:""};function _d(){const{t:s}=M("settings"),[n,a]=m.useState(!1),r=m.useRef(null),[l,c]=m.useState("singbox"),o=fe({resolver:ye(yd),defaultValues:Nd,mode:"onBlur"}),{data:u}=ie({queryKey:["settings","client"],queryFn:()=>fs("subscribe_template")}),{mutateAsync:x}=Ze({mutationFn:gs,onSuccess:f=>{f.data&&A.success(s("common.autoSaved"))}});m.useEffect(()=>{if(u?.data.subscribe_template){const f=u.data.subscribe_template;Object.entries(f).forEach(([w,P])=>{o.setValue(w,P)}),r.current=f}},[u]),console.log(o.getValues());const i=m.useCallback(Ce.debounce(async f=>{if(!Ce.isEqual(f,r.current)){a(!0);try{await x(f),r.current=f}finally{a(!1)}}},1e3),[x]),d=m.useCallback(f=>{i(f)},[i]);return e.jsx(Ne,{...o,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs(Wt,{value:l,onValueChange:c,children:[e.jsxs(bt,{children:[e.jsx(Ke,{value:"singbox",children:"Sing-box"}),e.jsx(Ke,{value:"clash",children:"Clash"}),e.jsx(Ke,{value:"clashmeta",children:"Clash Meta"}),e.jsx(Ke,{value:"stash",children:"Stash"}),e.jsx(Ke,{value:"surge",children:"Surge"})]}),e.jsx(Ns,{value:"singbox",children:e.jsx(b,{control:o.control,name:"subscribe_template_singbox",render:({field:f})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe_template.singbox.title")}),e.jsx(N,{children:e.jsx(it,{height:"500px",defaultLanguage:"json",value:f.value||"",onChange:w=>{typeof w=="string"&&(f.onChange(w),d(o.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(z,{children:s("subscribe_template.singbox.description")}),e.jsx(k,{})]})})}),e.jsx(Ns,{value:"clash",children:e.jsx(b,{control:o.control,name:"subscribe_template_clash",render:({field:f})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe_template.clash.title")}),e.jsx(N,{children:e.jsx(it,{height:"500px",defaultLanguage:"yaml",value:f.value||"",onChange:w=>{typeof w=="string"&&(f.onChange(w),d(o.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(z,{children:s("subscribe_template.clash.description")}),e.jsx(k,{})]})})}),e.jsx(Ns,{value:"clashmeta",children:e.jsx(b,{control:o.control,name:"subscribe_template_clashmeta",render:({field:f})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe_template.clashmeta.title")}),e.jsx(N,{children:e.jsx(it,{height:"500px",defaultLanguage:"yaml",value:f.value||"",onChange:w=>{typeof w=="string"&&(f.onChange(w),d(o.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(z,{children:s("subscribe_template.clashmeta.description")}),e.jsx(k,{})]})})}),e.jsx(Ns,{value:"stash",children:e.jsx(b,{control:o.control,name:"subscribe_template_stash",render:({field:f})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe_template.stash.title")}),e.jsx(N,{children:e.jsx(it,{height:"500px",defaultLanguage:"yaml",value:f.value||"",onChange:w=>{typeof w=="string"&&(f.onChange(w),d(o.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(z,{children:s("subscribe_template.stash.description")}),e.jsx(k,{})]})})}),e.jsx(Ns,{value:"surge",children:e.jsx(b,{control:o.control,name:"subscribe_template_surge",render:({field:f})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe_template.surge.title")}),e.jsx(N,{children:e.jsx(it,{height:"500px",defaultLanguage:"ini",value:f.value||"",onChange:w=>{typeof w=="string"&&(f.onChange(w),d(o.getValues()))},options:{minimap:{enabled:!1},fontSize:14}})}),e.jsx(z,{children:s("subscribe_template.surge.description")}),e.jsx(k,{})]})})})]}),n&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("common.saving")})]})})}function wd(){const{t:s}=M("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("subscribe_template.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("subscribe_template.description")})]}),e.jsx(Se,{}),e.jsx(_d,{})]})}const Cd=()=>e.jsx(dc,{children:e.jsx(Ca,{})}),Sd=Vl([{path:"/sign-in",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Hd);return{default:s}},void 0,import.meta.url)).default})},{element:e.jsx(Cd,{}),children:[{path:"/",lazy:async()=>({Component:(await ue(()=>Promise.resolve().then(()=>Zd),void 0,import.meta.url)).default}),errorElement:e.jsx(Gs,{}),children:[{index:!0,lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Nu);return{default:s}},void 0,import.meta.url)).default})},{path:"config",errorElement:e.jsx(Gs,{}),children:[{path:"system",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Su);return{default:s}},void 0,import.meta.url)).default}),children:[{index:!0,lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Pu);return{default:s}},void 0,import.meta.url)).default})},{path:"safe",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Mu);return{default:s}},void 0,import.meta.url)).default})},{path:"subscribe",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Au);return{default:s}},void 0,import.meta.url)).default})},{path:"invite",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Ku);return{default:s}},void 0,import.meta.url)).default})},{path:"frontend",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Qu);return{default:s}},void 0,import.meta.url)).default})},{path:"server",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>sm);return{default:s}},void 0,import.meta.url)).default})},{path:"email",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>lm);return{default:s}},void 0,import.meta.url)).default})},{path:"telegram",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>um);return{default:s}},void 0,import.meta.url)).default})},{path:"APP",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>fm);return{default:s}},void 0,import.meta.url)).default})},{path:"subscribe-template",element:e.jsx(wd,{})}]},{path:"payment",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Sm);return{default:s}},void 0,import.meta.url)).default})},{path:"plugin",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Dm);return{default:s}},void 0,import.meta.url)).default})},{path:"theme",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Im);return{default:s}},void 0,import.meta.url)).default})},{path:"notice",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Am);return{default:s}},void 0,import.meta.url)).default})},{path:"knowledge",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Wm);return{default:s}},void 0,import.meta.url)).default})}]},{path:"server",errorElement:e.jsx(Gs,{}),children:[{path:"manage",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>bx);return{default:s}},void 0,import.meta.url)).default})},{path:"group",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Cx);return{default:s}},void 0,import.meta.url)).default})},{path:"route",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Ex);return{default:s}},void 0,import.meta.url)).default})}]},{path:"finance",errorElement:e.jsx(Gs,{}),children:[{path:"plan",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Ax);return{default:s}},void 0,import.meta.url)).default})},{path:"order",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>eh);return{default:s}},void 0,import.meta.url)).default})},{path:"coupon",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>ch);return{default:s}},void 0,import.meta.url)).default})}]},{path:"user",errorElement:e.jsx(Gs,{}),children:[{path:"manage",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>Ah);return{default:s}},void 0,import.meta.url)).default})},{path:"ticket",lazy:async()=>({Component:(await ue(async()=>{const{default:s}=await Promise.resolve().then(()=>lp);return{default:s}},void 0,import.meta.url)).default})}]}]}]},{path:"/500",Component:Gs},{path:"/404",Component:en},{path:"/503",Component:ac},{path:"*",Component:en}]);function kd(){return V.get("/user/info")}const ra={token:ft()?.value||"",userInfo:null,isLoggedIn:!!ft()?.value,loading:!1,error:null},mt=Ml("user/fetchUserInfo",async()=>(await kd()).data,{condition:(s,{getState:n})=>{const{user:a}=n();return!!a.token&&!a.loading}}),kr=Fl({name:"user",initialState:ra,reducers:{setToken(s,n){s.token=n.payload,s.isLoggedIn=!!n.payload},resetUserState:()=>ra},extraReducers:s=>{s.addCase(mt.pending,n=>{n.loading=!0,n.error=null}).addCase(mt.fulfilled,(n,a)=>{n.loading=!1,n.userInfo=a.payload,n.error=null}).addCase(mt.rejected,(n,a)=>{if(n.loading=!1,n.error=a.error.message||"Failed to fetch user info",!n.token)return ra})}}),{setToken:Td,resetUserState:Dd}=kr.actions,Pd=s=>s.user.userInfo,Ed=kr.reducer,Tr=Ol({reducer:{user:Ed}});ft()?.value&&Tr.dispatch(mt());zl.use(Ll).use(Al).init({resources:{"en-US":window.XBOARD_TRANSLATIONS?.["en-US"]||{},"zh-CN":window.XBOARD_TRANSLATIONS?.["zh-CN"]||{},"ko-KR":window.XBOARD_TRANSLATIONS?.["ko-KR"]||{}},fallbackLng:"zh-CN",supportedLngs:["en-US","zh-CN","ko-KR"],detection:{order:["querystring","localStorage","navigator"],lookupQuerystring:"lang",lookupLocalStorage:"i18nextLng",caches:["localStorage"]},interpolation:{escapeValue:!1}});const Rd=new $l;ql.createRoot(document.getElementById("root")).render(e.jsx(Hl.StrictMode,{children:e.jsx(Ul,{client:Rd,children:e.jsx(Kl,{store:Tr,children:e.jsxs(Xo,{defaultTheme:"light",storageKey:"vite-ui-theme",children:[e.jsx(Bl,{router:Sd}),e.jsx(Gl,{richColors:!0,position:"top-right"})]})})})}));const Be=m.forwardRef(({className:s,...n},a)=>e.jsx("div",{ref:a,className:_("rounded-xl border bg-card text-card-foreground shadow",s),...n}));Be.displayName="Card";const es=m.forwardRef(({className:s,...n},a)=>e.jsx("div",{ref:a,className:_("flex flex-col space-y-1.5 p-6",s),...n}));es.displayName="CardHeader";const ws=m.forwardRef(({className:s,...n},a)=>e.jsx("h3",{ref:a,className:_("font-semibold leading-none tracking-tight",s),...n}));ws.displayName="CardTitle";const Zs=m.forwardRef(({className:s,...n},a)=>e.jsx("p",{ref:a,className:_("text-sm text-muted-foreground",s),...n}));Zs.displayName="CardDescription";const ss=m.forwardRef(({className:s,...n},a)=>e.jsx("div",{ref:a,className:_("p-6 pt-0",s),...n}));ss.displayName="CardContent";const Id=m.forwardRef(({className:s,...n},a)=>e.jsx("div",{ref:a,className:_("flex items-center p-6 pt-0",s),...n}));Id.displayName="CardFooter";const T=m.forwardRef(({className:s,type:n,...a},r)=>e.jsx("input",{type:n,className:_("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...a}));T.displayName="Input";const Dr=m.forwardRef(({className:s,...n},a)=>{const[r,l]=m.useState(!1);return e.jsxs("div",{className:"relative rounded-md",children:[e.jsx("input",{type:r?"text":"password",className:_("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",s),ref:a,...n}),e.jsx(D,{type:"button",size:"icon",variant:"ghost",className:"absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2 rounded-md text-muted-foreground",onClick:()=>l(c=>!c),children:r?e.jsx(Wl,{size:18}):e.jsx(Yl,{size:18})})]})});Dr.displayName="PasswordInput";const Vd=s=>V({url:"/passport/auth/login",method:"post",data:s});function Md({className:s,onForgotPassword:n,...a}){const r=ks(),l=yn(),{t:c}=M("auth"),o=h.object({email:h.string().min(1,{message:c("signIn.validation.emailRequired")}),password:h.string().min(1,{message:c("signIn.validation.passwordRequired")}).min(7,{message:c("signIn.validation.passwordLength")})}),u=fe({resolver:ye(o),defaultValues:{email:"",password:""}});async function x(i){try{const{data:d}=await Vd(i);jc(d.auth_data),l(Td(d.auth_data)),await l(mt()).unwrap(),r("/")}catch(d){console.error("Login failed:",d),d.response?.data?.message&&u.setError("root",{message:d.response.data.message})}}return e.jsx("div",{className:_("grid gap-6",s),...a,children:e.jsx(Ne,{...u,children:e.jsx("form",{onSubmit:u.handleSubmit(x),className:"space-y-4",children:e.jsxs("div",{className:"space-y-4",children:[u.formState.errors.root&&e.jsx("div",{className:"text-sm text-destructive",children:u.formState.errors.root.message}),e.jsx(b,{control:u.control,name:"email",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:c("signIn.email")}),e.jsx(N,{children:e.jsx(T,{placeholder:c("signIn.emailPlaceholder"),autoComplete:"email",...i})}),e.jsx(k,{})]})}),e.jsx(b,{control:u.control,name:"password",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:c("signIn.password")}),e.jsx(N,{children:e.jsx(Dr,{placeholder:c("signIn.passwordPlaceholder"),autoComplete:"current-password",...i})}),e.jsx(k,{})]})}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(D,{variant:"link",type:"button",className:"px-0 text-sm font-normal text-muted-foreground hover:text-primary",onClick:n,children:c("signIn.forgotPassword")})}),e.jsx(D,{className:"w-full",size:"lg",loading:u.formState.isSubmitting,children:c("signIn.submit")})]})})})})}const ve=Nn,Ye=_n,Fd=wn,Nt=Sa,Pr=m.forwardRef(({className:s,...n},a)=>e.jsx($t,{ref:a,className:_("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...n}));Pr.displayName=$t.displayName;const pe=m.forwardRef(({className:s,children:n,...a},r)=>e.jsxs(Fd,{children:[e.jsx(Pr,{}),e.jsxs(qt,{ref:r,className:_("max-h-[95%] overflow-auto fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...a,children:[n,e.jsxs(Sa,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[e.jsx(Xe,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));pe.displayName=qt.displayName;const we=({className:s,...n})=>e.jsx("div",{className:_("flex flex-col space-y-1.5 text-center sm:text-left",s),...n});we.displayName="DialogHeader";const Oe=({className:s,...n})=>e.jsx("div",{className:_("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...n});Oe.displayName="DialogFooter";const be=m.forwardRef(({className:s,...n},a)=>e.jsx(Ht,{ref:a,className:_("text-lg font-semibold leading-none tracking-tight",s),...n}));be.displayName=Ht.displayName;const ke=m.forwardRef(({className:s,...n},a)=>e.jsx(Ut,{ref:a,className:_("text-sm text-muted-foreground",s),...n}));ke.displayName=Ut.displayName;const Xs=Ls("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),X=m.forwardRef(({className:s,variant:n,size:a,asChild:r=!1,...l},c)=>{const o=r?_a:"button";return e.jsx(o,{className:_(Xs({variant:n,size:a,className:s})),ref:c,...l})});X.displayName="Button";const Cs=Zl,Ss=Xl,Od=ei,zd=m.forwardRef(({className:s,inset:n,children:a,...r},l)=>e.jsxs(Cn,{ref:l,className:_("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",n&&"pl-8",s),...r,children:[a,e.jsx(ka,{className:"ml-auto h-4 w-4"})]}));zd.displayName=Cn.displayName;const Ld=m.forwardRef(({className:s,...n},a)=>e.jsx(Sn,{ref:a,className:_("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...n}));Ld.displayName=Sn.displayName;const ps=m.forwardRef(({className:s,sideOffset:n=4,...a},r)=>e.jsx(Ql,{children:e.jsx(kn,{ref:r,sideOffset:n,className:_("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...a})}));ps.displayName=kn.displayName;const ge=m.forwardRef(({className:s,inset:n,...a},r)=>e.jsx(Tn,{ref:r,className:_("relative flex cursor-default cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n&&"pl-8",s),...a}));ge.displayName=Tn.displayName;const Ad=m.forwardRef(({className:s,children:n,checked:a,...r},l)=>e.jsxs(Dn,{ref:l,className:_("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:a,...r,children:[e.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(Pn,{children:e.jsx(As,{className:"h-4 w-4"})})}),n]}));Ad.displayName=Dn.displayName;const $d=m.forwardRef(({className:s,children:n,...a},r)=>e.jsxs(En,{ref:r,className:_("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...a,children:[e.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(Pn,{children:e.jsx(Jl,{className:"h-4 w-4 fill-current"})})}),n]}));$d.displayName=En.displayName;const Ia=m.forwardRef(({className:s,inset:n,...a},r)=>e.jsx(Rn,{ref:r,className:_("px-2 py-1.5 text-sm font-semibold",n&&"pl-8",s),...a}));Ia.displayName=Rn.displayName;const et=m.forwardRef(({className:s,...n},a)=>e.jsx(In,{ref:a,className:_("-mx-1 my-1 h-px bg-muted",s),...n}));et.displayName=In.displayName;const ba=({className:s,...n})=>e.jsx("span",{className:_("ml-auto text-xs tracking-widest opacity-60",s),...n});ba.displayName="DropdownMenuShortcut";const la=[{code:"en-US",name:"English",flag:si,shortName:"EN"},{code:"zh-CN",name:"中文",flag:ti,shortName:"CN"},{code:"ko-KR",name:"한국어",flag:ai,shortName:"KR"}];function Er(){const{i18n:s}=M(),n=l=>{s.changeLanguage(l)},a=la.find(l=>l.code===s.language)||la[1],r=a.flag;return e.jsxs(Cs,{children:[e.jsx(Ss,{asChild:!0,children:e.jsxs(X,{variant:"ghost",size:"sm",className:"h-8 px-2 gap-1",children:[e.jsx(r,{className:"h-4 w-5 rounded-sm shadow-sm"}),e.jsx("span",{className:"text-sm font-medium",children:a.shortName})]})}),e.jsx(ps,{align:"end",className:"w-[120px]",children:la.map(l=>{const c=l.flag,o=l.code===s.language;return e.jsxs(ge,{onClick:()=>n(l.code),className:_("flex items-center gap-2 px-2 py-1.5 cursor-pointer",o&&"bg-accent"),children:[e.jsx(c,{className:"h-4 w-5 rounded-sm shadow-sm"}),e.jsx("span",{className:_("text-sm",o&&"font-medium"),children:l.name})]},l.code)})})]})}function qd(){const[s,n]=m.useState(!1),{t:a}=M("auth"),r=a("signIn.resetPassword.command");return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"container relative grid h-svh flex-col items-center justify-center bg-primary-foreground lg:max-w-none lg:px-0",children:[e.jsx("div",{className:"absolute right-4 top-4 md:right-8 md:top-8",children:e.jsx(Er,{})}),e.jsxs("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[480px] lg:p-8",children:[e.jsxs("div",{className:"flex flex-col space-y-2 text-center",children:[e.jsx("h1",{className:"text-3xl font-bold",children:window?.settings?.title}),e.jsx("p",{className:"text-sm text-muted-foreground",children:window?.settings?.description})]}),e.jsxs(Be,{className:"p-6",children:[e.jsxs("div",{className:"flex flex-col space-y-2 text-left",children:[e.jsx("h1",{className:"text-2xl font-semibold tracking-tight",children:a("signIn.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:a("signIn.description")})]}),e.jsx(Md,{onForgotPassword:()=>n(!0)})]})]})]}),e.jsx(ve,{open:s,onOpenChange:n,children:e.jsx(pe,{children:e.jsxs(we,{children:[e.jsx(be,{children:a("signIn.resetPassword.title")}),e.jsx(ke,{children:a("signIn.resetPassword.description")}),e.jsx("div",{className:"mt-4",children:e.jsxs("div",{className:"relative",children:[e.jsx("pre",{className:"rounded-md bg-secondary p-4 pr-12 text-sm",children:r}),e.jsx(X,{variant:"ghost",size:"icon",className:"absolute right-2 top-2 h-8 w-8 hover:bg-secondary-foreground/10",onClick:()=>zt(r).then(()=>{A.success(a("common:copy.success"))}),children:e.jsx(ni,{className:"h-4 w-4"})})]})})]})})})]})}const Hd=Object.freeze(Object.defineProperty({__proto__:null,default:qd},Symbol.toStringTag,{value:"Module"})),Pe=m.forwardRef(({className:s,fadedBelow:n=!1,fixedHeight:a=!1,...r},l)=>e.jsx("div",{ref:l,className:_("relative flex h-full w-full flex-col",n&&"after:pointer-events-none after:absolute after:bottom-0 after:left-0 after:hidden after:h-32 after:w-full after:bg-[linear-gradient(180deg,_transparent_10%,_hsl(var(--background))_70%)] after:md:block",a&&"md:h-svh",s),...r}));Pe.displayName="Layout";const Ee=m.forwardRef(({className:s,...n},a)=>e.jsx("div",{ref:a,className:_("flex h-[var(--header-height)] flex-none items-center gap-4 bg-background p-4 md:px-8",s),...n}));Ee.displayName="LayoutHeader";const Ve=m.forwardRef(({className:s,fixedHeight:n,...a},r)=>e.jsx("div",{ref:r,className:_("flex-1 overflow-hidden px-4 py-6 md:px-8",n&&"h-[calc(100%-var(--header-height))]",s),...a}));Ve.displayName="LayoutBody";const Rr=ri,Ir=li,Vr=ii,je=oi,xe=ci,he=di,ce=m.forwardRef(({className:s,sideOffset:n=4,...a},r)=>e.jsx(Vn,{ref:r,sideOffset:n,className:_("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...a}));ce.displayName=Vn.displayName;function Yt(){const{pathname:s}=wa();return{checkActiveNav:a=>{if(a==="/"&&s==="/")return!0;const r=a.replace(/^\//,""),l=s.replace(/^\//,"");return r?l.startsWith(r):!1}}}function Mr({key:s,defaultValue:n}){const[a,r]=m.useState(()=>{const l=localStorage.getItem(s);return l!==null?JSON.parse(l):n});return m.useEffect(()=>{localStorage.setItem(s,JSON.stringify(a))},[a,s]),[a,r]}function Ud(){const[s,n]=Mr({key:"collapsed-sidebar-items",defaultValue:[]}),a=l=>!s.includes(l);return{isExpanded:a,toggleItem:l=>{a(l)?n([...s,l]):n(s.filter(c=>c!==l))}}}function Kd({links:s,isCollapsed:n,className:a,closeNav:r}){const{t:l}=M(),c=({sub:o,...u})=>{const x=`${l(u.title)}-${u.href}`;return n&&o?m.createElement(Wd,{...u,sub:o,key:x,closeNav:r}):n?m.createElement(Gd,{...u,key:x,closeNav:r}):o?m.createElement(Bd,{...u,sub:o,key:x,closeNav:r}):m.createElement(Fr,{...u,key:x,closeNav:r})};return e.jsx("div",{"data-collapsed":n,className:_("group border-b bg-background py-2 transition-[max-height,padding] duration-500 data-[collapsed=true]:py-2 md:border-none",a),children:e.jsx(je,{delayDuration:0,children:e.jsx("nav",{className:"grid gap-1 group-[[data-collapsed=true]]:justify-center group-[[data-collapsed=true]]:px-2",children:s.map(c)})})})}function Fr({title:s,icon:n,label:a,href:r,closeNav:l,subLink:c=!1}){const{checkActiveNav:o}=Yt(),{t:u}=M();return e.jsxs($s,{to:r,onClick:l,className:_(tt({variant:o(r)?"secondary":"ghost",size:"sm"}),"h-12 justify-start text-wrap rounded-none px-6",c&&"h-10 w-full border-l border-l-slate-500 px-2"),"aria-current":o(r)?"page":void 0,children:[e.jsx("div",{className:"mr-2",children:n}),u(s),a&&e.jsx("div",{className:"ml-2 rounded-lg bg-primary px-1 text-[0.625rem] text-primary-foreground",children:u(a)})]})}function Bd({title:s,icon:n,label:a,sub:r,closeNav:l}){const{checkActiveNav:c}=Yt(),{isExpanded:o,toggleItem:u}=Ud(),{t:x}=M(),i=!!r?.find(w=>c(w.href)),d=x(s),f=o(d)||i;return e.jsxs(Rr,{open:f,onOpenChange:()=>u(d),children:[e.jsxs(Ir,{className:_(tt({variant:i?"secondary":"ghost",size:"sm"}),"group h-12 w-full justify-start rounded-none px-6"),children:[e.jsx("div",{className:"mr-2",children:n}),x(s),a&&e.jsx("div",{className:"ml-2 rounded-lg bg-primary px-1 text-[0.625rem] text-primary-foreground",children:x(a)}),e.jsx("span",{className:_('ml-auto transition-all group-data-[state="open"]:-rotate-180'),children:e.jsx(Mn,{stroke:1})})]}),e.jsx(Vr,{className:"collapsibleDropdown",asChild:!0,children:e.jsx("ul",{children:r.map(w=>e.jsx("li",{className:"my-1 ml-8",children:e.jsx(Fr,{...w,subLink:!0,closeNav:l})},x(w.title)))})})]})}function Gd({title:s,icon:n,label:a,href:r,closeNav:l}){const{checkActiveNav:c}=Yt(),{t:o}=M();return e.jsxs(xe,{delayDuration:0,children:[e.jsx(he,{asChild:!0,children:e.jsxs($s,{to:r,onClick:l,className:_(tt({variant:c(r)?"secondary":"ghost",size:"icon"}),"h-12 w-12"),children:[n,e.jsx("span",{className:"sr-only",children:o(s)})]})}),e.jsxs(ce,{side:"right",className:"flex items-center gap-4",children:[o(s),a&&e.jsx("span",{className:"ml-auto text-muted-foreground",children:o(a)})]})]})}function Wd({title:s,icon:n,label:a,sub:r,closeNav:l}){const{checkActiveNav:c}=Yt(),{t:o}=M(),u=!!r?.find(x=>c(x.href));return e.jsxs(Cs,{children:[e.jsxs(xe,{delayDuration:0,children:[e.jsx(he,{asChild:!0,children:e.jsx(Ss,{asChild:!0,children:e.jsx(D,{variant:u?"secondary":"ghost",size:"icon",className:"h-12 w-12",children:n})})}),e.jsxs(ce,{side:"right",className:"flex items-center gap-4",children:[o(s)," ",a&&e.jsx("span",{className:"ml-auto text-muted-foreground",children:o(a)}),e.jsx(Mn,{size:18,className:"-rotate-90 text-muted-foreground"})]})]}),e.jsxs(ps,{side:"right",align:"start",sideOffset:4,children:[e.jsxs(Ia,{children:[o(s)," ",a?`(${o(a)})`:""]}),e.jsx(et,{}),r.map(({title:x,icon:i,label:d,href:f})=>e.jsx(ge,{asChild:!0,children:e.jsxs($s,{to:f,onClick:l,className:`${c(f)?"bg-secondary":""}`,children:[i," ",e.jsx("span",{className:"ml-2 max-w-52 text-wrap",children:o(x)}),d&&e.jsx("span",{className:"ml-auto text-xs",children:o(d)})]})},`${o(x)}-${f}`))]})]})}const Or=[{title:"nav:dashboard",label:"",href:"/",icon:e.jsx(ui,{size:18})},{title:"nav:systemManagement",label:"",href:"",icon:e.jsx(mi,{size:18}),sub:[{title:"nav:systemConfig",label:"",href:"/config/system",icon:e.jsx(Fn,{size:18})},{title:"nav:pluginManagement",label:"",href:"/config/plugin",icon:e.jsx(Ta,{size:18})},{title:"nav:themeConfig",label:"",href:"/config/theme",icon:e.jsx(xi,{size:18})},{title:"nav:noticeManagement",label:"",href:"/config/notice",icon:e.jsx(hi,{size:18})},{title:"nav:paymentConfig",label:"",href:"/config/payment",icon:e.jsx(Ba,{size:18})},{title:"nav:knowledgeManagement",label:"",href:"/config/knowledge",icon:e.jsx(pi,{size:18})}]},{title:"nav:nodeManagement",label:"",href:"",icon:e.jsx(On,{size:18}),sub:[{title:"nav:nodeManagement",label:"",href:"/server/manage",icon:e.jsx(fi,{size:18})},{title:"nav:permissionGroupManagement",label:"",href:"/server/group",icon:e.jsx(zn,{size:18})},{title:"nav:routeManagement",label:"",href:"/server/route",icon:e.jsx(gi,{size:18})}]},{title:"nav:subscriptionManagement",label:"",href:"",icon:e.jsx(ji,{size:18}),sub:[{title:"nav:planManagement",label:"",href:"/finance/plan",icon:e.jsx(vi,{size:18})},{title:"nav:orderManagement",label:"",href:"/finance/order",icon:e.jsx(Ba,{size:18})},{title:"nav:couponManagement",label:"",href:"/finance/coupon",icon:e.jsx(bi,{size:18})}]},{title:"nav:userManagement",label:"",href:"",icon:e.jsx(yi,{size:18}),sub:[{title:"nav:userManagement",label:"",href:"/user/manage",icon:e.jsx(Ni,{size:18})},{title:"nav:ticketManagement",label:"",href:"/user/ticket",icon:e.jsx(Ln,{size:18})}]}];function Yd({className:s,isCollapsed:n,setIsCollapsed:a}){const[r,l]=m.useState(!1),{t:c}=M();return m.useEffect(()=>{r?document.body.classList.add("overflow-hidden"):document.body.classList.remove("overflow-hidden")},[r]),e.jsxs("aside",{className:_(`fixed left-0 right-0 top-0 z-50 flex h-auto flex-col border-r-2 border-r-muted transition-[width] md:bottom-0 md:right-auto md:h-svh ${n?"md:w-14":"md:w-64"}`,s),children:[e.jsx("div",{onClick:()=>l(!1),className:`absolute inset-0 transition-[opacity] delay-100 duration-700 ${r?"h-svh opacity-50":"h-0 opacity-0"} w-full bg-black md:hidden`}),e.jsxs(Pe,{className:"flex h-full flex-col",children:[e.jsxs(Ee,{className:"sticky top-0 justify-between px-4 py-3 shadow md:px-4",children:[e.jsxs("div",{className:`flex items-center ${n?"":"gap-2"}`,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",className:`transition-all ${n?"h-6 w-6":"h-8 w-8"}`,children:[e.jsx("rect",{width:"256",height:"256",fill:"none"}),e.jsx("line",{x1:"208",y1:"128",x2:"128",y2:"208",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),e.jsx("line",{x1:"192",y1:"40",x2:"40",y2:"192",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"16"}),e.jsx("span",{className:"sr-only",children:"Website Name"})]}),e.jsx("div",{className:`flex flex-col justify-end truncate ${n?"invisible w-0":"visible w-auto"}`,children:e.jsx("span",{className:"font-medium",children:window?.settings?.title})})]}),e.jsx(D,{variant:"ghost",size:"icon",className:"md:hidden","aria-label":c("common:toggleNavigation"),"aria-controls":"sidebar-menu","aria-expanded":r,onClick:()=>l(o=>!o),children:r?e.jsx(_i,{}):e.jsx(wi,{})})]}),e.jsx(Kd,{id:"sidebar-menu",className:_("flex-1 overflow-auto",r?"block":"hidden md:block","md:py-2"),closeNav:()=>l(!1),isCollapsed:n,links:Or}),e.jsx("div",{className:_("border-t border-border/50 bg-background","px-4 py-2.5 text-xs text-muted-foreground",r?"block":"hidden md:block",n?"text-center":"text-left"),children:e.jsxs("div",{className:_("flex items-center gap-1.5",n?"justify-center":"justify-start"),children:[e.jsx("div",{className:"w-1.5 h-1.5 rounded-full bg-green-500"}),e.jsxs("span",{className:_("tracking-wide whitespace-nowrap","transition-opacity duration-200",n&&"md:opacity-0"),children:["v",window?.settings?.version]})]})}),e.jsx(D,{onClick:()=>a(o=>!o),size:"icon",variant:"outline",className:"absolute -right-5 top-1/2 hidden rounded-full md:inline-flex","aria-label":c("common:toggleSidebar"),children:e.jsx(Ci,{stroke:1.5,className:`h-5 w-5 ${n?"rotate-180":""}`})})]})]})}function Qd(){const[s,n]=Mr({key:"collapsed-sidebar",defaultValue:!1});return m.useEffect(()=>{const a=()=>{n(window.innerWidth<768?!1:s)};return a(),window.addEventListener("resize",a),()=>{window.removeEventListener("resize",a)}},[s,n]),[s,n]}function Jd(){const[s,n]=Qd();return e.jsxs("div",{className:"relative h-full overflow-hidden bg-background",children:[e.jsx(Yd,{isCollapsed:s,setIsCollapsed:n}),e.jsx("main",{id:"content",className:`overflow-x-hidden pt-16 transition-[margin] md:overflow-y-hidden md:pt-0 ${s?"md:ml-14":"md:ml-64"} h-full`,children:e.jsx(Ca,{})})]})}const Zd=Object.freeze(Object.defineProperty({__proto__:null,default:Jd},Symbol.toStringTag,{value:"Module"})),Vs=m.forwardRef(({className:s,...n},a)=>e.jsx(Fe,{ref:a,className:_("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",s),...n}));Vs.displayName=Fe.displayName;const Xd=({children:s,...n})=>e.jsx(ve,{...n,children:e.jsx(pe,{className:"overflow-hidden p-0",children:e.jsx(Vs,{className:"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:s})})}),Us=m.forwardRef(({className:s,...n},a)=>e.jsxs("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[e.jsx(Si,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),e.jsx(Fe.Input,{ref:a,className:_("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",s),...n})]}));Us.displayName=Fe.Input.displayName;const Ms=m.forwardRef(({className:s,...n},a)=>e.jsx(Fe.List,{ref:a,className:_("max-h-[300px] overflow-y-auto overflow-x-hidden",s),...n}));Ms.displayName=Fe.List.displayName;const Ks=m.forwardRef((s,n)=>e.jsx(Fe.Empty,{ref:n,className:"py-6 text-center text-sm",...s}));Ks.displayName=Fe.Empty.displayName;const Ge=m.forwardRef(({className:s,...n},a)=>e.jsx(Fe.Group,{ref:a,className:_("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",s),...n}));Ge.displayName=Fe.Group.displayName;const at=m.forwardRef(({className:s,...n},a)=>e.jsx(Fe.Separator,{ref:a,className:_("-mx-1 h-px bg-border",s),...n}));at.displayName=Fe.Separator.displayName;const Ie=m.forwardRef(({className:s,...n},a)=>e.jsx(Fe.Item,{ref:a,className:_("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n}));Ie.displayName=Fe.Item.displayName;function eu(){const s=[];for(const n of Or)if(n.href&&s.push(n),n.sub)for(const a of n.sub)s.push({...a,parent:n.title});return s}function qe(){const[s,n]=m.useState(!1),a=ks(),r=eu(),{t:l}=M("search"),{t:c}=M("nav");m.useEffect(()=>{const u=x=>{x.key==="k"&&(x.metaKey||x.ctrlKey)&&(x.preventDefault(),n(i=>!i))};return document.addEventListener("keydown",u),()=>document.removeEventListener("keydown",u)},[]);const o=m.useCallback(u=>{n(!1),a(u)},[a]);return e.jsxs(e.Fragment,{children:[e.jsxs(X,{variant:"outline",className:"relative h-9 w-9 p-0 xl:h-10 xl:w-60 xl:justify-start xl:px-3 xl:py-2",onClick:()=>n(!0),children:[e.jsx(An,{className:"h-4 w-4 xl:mr-2"}),e.jsx("span",{className:"hidden xl:inline-flex",children:l("placeholder")}),e.jsx("span",{className:"sr-only",children:l("shortcut.label")}),e.jsx("kbd",{className:"pointer-events-none absolute right-1.5 top-2 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 xl:flex",children:l("shortcut.key")})]}),e.jsxs(Xd,{open:s,onOpenChange:n,children:[e.jsx(Us,{placeholder:l("placeholder")}),e.jsxs(Ms,{children:[e.jsx(Ks,{children:l("noResults")}),e.jsx(Ge,{heading:l("title"),children:r.map(u=>e.jsxs(Ie,{value:`${u.parent?u.parent+" ":""}${u.title}`,onSelect:()=>o(u.href),children:[e.jsx("div",{className:"mr-2",children:u.icon}),e.jsx("span",{children:c(u.title)}),u.parent&&e.jsx("span",{className:"ml-2 text-xs text-muted-foreground",children:c(u.parent)})]},u.href))})]})]})]})}const le=window?.settings?.secure_path,zr=5*60*1e3,ya=new Map,su=s=>{const n=ya.get(s);return n?Date.now()-n.timestamp>zr?(ya.delete(s),null):n.data:null},tu=(s,n)=>{ya.set(s,{data:n,timestamp:Date.now()})},au=async(s,n=zr)=>{const a=su(s);if(a)return a;const r=await V.get(s);return tu(s,r),r},Lt={getList:s=>V.post(`${le}/user/fetch`,s),update:s=>V.post(`${le}/user/update`,s),resetSecret:s=>V.post(`${le}/user/resetSecret`,{id:s}),generate:s=>V.post(`${le}/user/generate`,s),getStats:s=>V.post(`${le}/stat/getStatUser`,s),destroy:s=>V.post(`${le}/user/destroy`,{id:s}),sendMail:s=>V.post(`${le}/user/sendMail`,s),dumpCSV:s=>V.post(`${le}/user/dumpCSV`,s,{responseType:"blob"}),batchBan:s=>V.post(`${le}/user/ban`,s)},nn={getList:()=>au(`${le}/notice/fetch`),save:s=>V.post(`${le}/notice/save`,s),drop:s=>V.post(`${le}/notice/drop`,{id:s}),updateStatus:s=>V.post(`${le}/notice/show`,{id:s}),sort:s=>V.post(`${le}/notice/sort`,{ids:s})},ia={getList:s=>V.post(`${le}/ticket/fetch`,s),getInfo:s=>V.get(`${le}/ticket/fetch?id=${s}`),reply:s=>V.post(`${le}/ticket/reply`,s),close:s=>V.post(`${le}/ticket/close`,{id:s})},rn={getSystemStatus:()=>V.get(`${le}/system/getSystemStatus`),getQueueStats:()=>V.get(`${le}/system/getQueueStats`),getQueueWorkload:()=>V.get(`${le}/system/getQueueWorkload`),getQueueMasters:()=>V.get(`${le}/system/getQueueMasters`),getSystemLog:s=>V.get(`${le}/system/getSystemLog`,{params:s})},ys={getPluginList:()=>V.get(`${le}/plugin/getPlugins`),uploadPlugin:s=>{const n=new FormData;return n.append("file",s),V.post(`${le}/plugin/upload`,n,{headers:{"Content-Type":"multipart/form-data"}})},deletePlugin:s=>V.post(`${le}/plugin/delete`,{code:s}),installPlugin:s=>V.post(`${le}/plugin/install`,{code:s}),uninstallPlugin:s=>V.post(`${le}/plugin/uninstall`,{code:s}),enablePlugin:s=>V.post(`${le}/plugin/enable`,{code:s}),disablePlugin:s=>V.post(`${le}/plugin/disable`,{code:s}),getPluginConfig:s=>V.get(`${le}/plugin/config`,{params:{code:s}}),updatePluginConfig:(s,n)=>V.post(`${le}/plugin/config`,{code:s,config:n})},nu=()=>V.get(`${le}/update/check`),ru=()=>V.post(`${le}/update/execute`),lu=Ls("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/10",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function K({className:s,variant:n,...a}){return e.jsx("div",{className:_(lu({variant:n}),s),...a})}const zs=m.forwardRef(({className:s,children:n,...a},r)=>e.jsxs($n,{ref:r,className:_("relative overflow-hidden",s),...a,children:[e.jsx(ki,{className:"h-full w-full rounded-[inherit]",children:n}),e.jsx(At,{}),e.jsx(Ti,{})]}));zs.displayName=$n.displayName;const At=m.forwardRef(({className:s,orientation:n="vertical",...a},r)=>e.jsx(qn,{ref:r,orientation:n,className:_("flex touch-none select-none transition-colors",n==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",n==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",s),...a,children:e.jsx(Di,{className:"relative flex-1 rounded-full bg-border"})}));At.displayName=qn.displayName;function iu(){const{t:s}=M("common"),[n,a]=m.useState(!1),{data:r}=ie({queryKey:["checkUpdate"],queryFn:async()=>await nu(),refetchInterval:1e3*60*60}),l=r?.data,c=Ze({mutationFn:ru,onSuccess:o=>{const u=o.data;u.success?(A.success(s("update.updateSuccess")),a(!1)):A.error(u.message)},onError:()=>{A.error(s("update.updateFailed"))}});return l?.has_update?e.jsxs(e.Fragment,{children:[e.jsxs(D,{size:"icon",variant:"ghost",className:"rounded-full relative",onClick:()=>a(!0),children:[e.jsx(Pi,{size:20,className:"text-red-500"}),e.jsx("span",{className:"absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"})]}),e.jsx(ve,{open:n,onOpenChange:a,children:e.jsxs(pe,{className:"sm:max-w-[500px]",children:[e.jsxs(we,{children:[e.jsx(be,{children:s("update.title")}),e.jsxs(ke,{children:[s("update.newVersion")," ",l?.latest_version]})]}),e.jsxs("div",{className:"py-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[s("update.currentVersion"),"：",l?.current_version]}),e.jsxs(K,{variant:"outline",children:[s("update.latestVersion"),"：",l?.latest_version]})]}),e.jsx(zs,{className:"h-[200px] rounded-md border p-4",children:e.jsx("div",{className:"space-y-4",children:l?.update_logs.map(o=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(K,{variant:"secondary",children:o.version}),e.jsx("span",{className:"text-sm text-muted-foreground",children:Je(new Date(o.date),"yyyy-MM-dd HH:mm")})]}),e.jsx("p",{className:"text-sm",children:o.message})]},o.version))})})]}),e.jsxs(Oe,{children:[e.jsx(D,{variant:"outline",onClick:()=>a(!1),children:s("update.updateLater")}),e.jsx(D,{onClick:()=>c.mutate(),disabled:c.isPending,children:c.isPending?s("update.updating"):s("update.updateNow")})]})]})})]}):null}function ze(){const{theme:s,setTheme:n}=ec();return m.useEffect(()=>{const a=s==="dark"?"#020817":"#fff",r=document.querySelector("meta[name='theme-color']");r&&r.setAttribute("content",a)},[s]),e.jsxs(e.Fragment,{children:[e.jsx(D,{size:"icon",variant:"ghost",className:"rounded-full",onClick:()=>n(s==="light"?"dark":"light"),children:s==="light"?e.jsx(Ei,{size:20}):e.jsx(Ri,{size:20})}),e.jsx(Er,{}),e.jsx(iu,{})]})}const Lr=m.forwardRef(({className:s,...n},a)=>e.jsx(Hn,{ref:a,className:_("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...n}));Lr.displayName=Hn.displayName;const Ar=m.forwardRef(({className:s,...n},a)=>e.jsx(Un,{ref:a,className:_("aspect-square h-full w-full",s),...n}));Ar.displayName=Un.displayName;const $r=m.forwardRef(({className:s,...n},a)=>e.jsx(Kn,{ref:a,className:_("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...n}));$r.displayName=Kn.displayName;function Le(){const s=ks(),n=yn(),a=Ii(Pd),{t:r}=M(["common"]),l=()=>{yr(),n(Dd()),s("/sign-in")},c=a?.email?.split("@")[0]||r("common:user"),o=c.substring(0,2).toUpperCase();return e.jsxs(Cs,{children:[e.jsx(Ss,{asChild:!0,children:e.jsx(D,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:e.jsxs(Lr,{className:"h-8 w-8",children:[e.jsx(Ar,{src:a?.avatar_url,alt:c}),e.jsx($r,{children:o})]})})}),e.jsxs(ps,{className:"w-56",align:"end",forceMount:!0,children:[e.jsx(Ia,{className:"font-normal",children:e.jsxs("div",{className:"flex flex-col space-y-1",children:[e.jsx("p",{className:"text-sm font-medium leading-none",children:c}),e.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:a?.email||r("common:defaultEmail")})]})}),e.jsx(et,{}),e.jsx(ge,{asChild:!0,children:e.jsxs($s,{to:"/config/system",children:[r("common:settings"),e.jsx(ba,{children:"⌘S"})]})}),e.jsx(et,{}),e.jsxs(ge,{onClick:l,children:[r("common:logout"),e.jsx(ba,{children:"⇧⌘Q"})]})]})]})}const J=Vi,ns=qi,Z=Mi,W=m.forwardRef(({className:s,children:n,...a},r)=>e.jsxs(Bn,{ref:r,className:_("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...a,children:[n,e.jsx(Fi,{asChild:!0,children:e.jsx(Da,{className:"h-4 w-4 opacity-50"})})]}));W.displayName=Bn.displayName;const qr=m.forwardRef(({className:s,...n},a)=>e.jsx(Gn,{ref:a,className:_("flex cursor-default items-center justify-center py-1",s),...n,children:e.jsx(Oi,{className:"h-4 w-4"})}));qr.displayName=Gn.displayName;const Hr=m.forwardRef(({className:s,...n},a)=>e.jsx(Wn,{ref:a,className:_("flex cursor-default items-center justify-center py-1",s),...n,children:e.jsx(Da,{className:"h-4 w-4"})}));Hr.displayName=Wn.displayName;const Y=m.forwardRef(({className:s,children:n,position:a="popper",...r},l)=>e.jsx(zi,{children:e.jsxs(Yn,{ref:l,className:_("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:a,...r,children:[e.jsx(qr,{}),e.jsx(Li,{className:_("p-1",a==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),e.jsx(Hr,{})]})}));Y.displayName=Yn.displayName;const ou=m.forwardRef(({className:s,...n},a)=>e.jsx(Qn,{ref:a,className:_("px-2 py-1.5 text-sm font-semibold",s),...n}));ou.displayName=Qn.displayName;const q=m.forwardRef(({className:s,children:n,...a},r)=>e.jsxs(Jn,{ref:r,className:_("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...a,children:[e.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(Ai,{children:e.jsx(As,{className:"h-4 w-4"})})}),e.jsx($i,{children:n})]}));q.displayName=Jn.displayName;const cu=m.forwardRef(({className:s,...n},a)=>e.jsx(Zn,{ref:a,className:_("-mx-1 my-1 h-px bg-muted",s),...n}));cu.displayName=Zn.displayName;function Bs({className:s,classNames:n,showOutsideDays:a=!0,...r}){return e.jsx(Hi,{showOutsideDays:a,className:_("p-3",s),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:_(Xs({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:_("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md",r.mode==="range"?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:_(Xs({variant:"ghost"}),"h-8 w-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start",day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...n},components:{IconLeft:({className:l,...c})=>e.jsx(Xn,{className:_("h-4 w-4",l),...c}),IconRight:({className:l,...c})=>e.jsx(ka,{className:_("h-4 w-4",l),...c})},...r})}Bs.displayName="Calendar";const js=Ki,vs=Bi,ms=m.forwardRef(({className:s,align:n="center",sideOffset:a=4,...r},l)=>e.jsx(Ui,{children:e.jsx(er,{ref:l,align:n,sideOffset:a,className:_("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...r})}));ms.displayName=er.displayName;const Ps={income:{main:"hsl(var(--primary))",gradient:{start:"hsl(var(--primary))",end:"transparent"}},commission:{main:"hsl(var(--secondary))",gradient:{start:"hsl(var(--secondary))",end:"transparent"}}},dt=s=>(s/100).toFixed(2),du=({active:s,payload:n,label:a})=>{const{t:r}=M();return s&&n&&n.length?e.jsxs("div",{className:"rounded-lg border bg-background p-3 shadow-sm",children:[e.jsx("div",{className:"mb-2 text-sm font-medium",children:a}),n.map((l,c)=>e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx("div",{className:"h-2 w-2 rounded-full",style:{backgroundColor:l.color}}),e.jsxs("span",{className:"text-muted-foreground",children:[r(l.name),":"]}),e.jsx("span",{className:"font-medium",children:l.name.includes(r("dashboard:overview.amount"))?`¥${dt(l.value)}`:r("dashboard:overview.transactions",{count:l.value})})]},c))]}):null},uu=[{value:"7d",label:"dashboard:overview.last7Days"},{value:"30d",label:"dashboard:overview.last30Days"},{value:"90d",label:"dashboard:overview.last90Days"},{value:"180d",label:"dashboard:overview.last180Days"},{value:"365d",label:"dashboard:overview.lastYear"},{value:"custom",label:"dashboard:overview.customRange"}],mu=(s,n)=>{const a=new Date;if(s==="custom"&&n)return{startDate:n.from,endDate:n.to};let r;switch(s){case"7d":r=ls(a,7);break;case"30d":r=ls(a,30);break;case"90d":r=ls(a,90);break;case"180d":r=ls(a,180);break;case"365d":r=ls(a,365);break;default:r=ls(a,30)}return{startDate:r,endDate:a}};function xu(){const[s,n]=m.useState("amount"),[a,r]=m.useState("30d"),[l,c]=m.useState({from:ls(new Date,7),to:new Date}),{t:o}=M(),{startDate:u,endDate:x}=mu(a,l),{data:i}=ie({queryKey:["orderStat",{start_date:Je(u,"yyyy-MM-dd"),end_date:Je(x,"yyyy-MM-dd")}],queryFn:async()=>{const{data:d}=await vc({start_date:Je(u,"yyyy-MM-dd"),end_date:Je(x,"yyyy-MM-dd")});return d},refetchInterval:3e4});return e.jsxs(Be,{children:[e.jsx(es,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(ws,{children:o("dashboard:overview.title")}),e.jsxs(Zs,{children:[i?.summary.start_date," ",o("dashboard:overview.to")," ",i?.summary.end_date]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex min-w-0 items-center gap-1",children:[e.jsxs(J,{value:a,onValueChange:d=>r(d),children:[e.jsx(W,{className:"w-[120px]",children:e.jsx(Z,{placeholder:o("dashboard:overview.selectTimeRange")})}),e.jsx(Y,{children:uu.map(d=>e.jsx(q,{value:d.value,children:o(d.label)},d.value))})]}),a==="custom"&&e.jsxs(js,{children:[e.jsx(vs,{asChild:!0,children:e.jsxs(X,{variant:"outline",className:_("min-w-0 justify-start text-left font-normal",!l&&"text-muted-foreground"),children:[e.jsx(jt,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:l?.from?l.to?e.jsxs(e.Fragment,{children:[Je(l.from,"yyyy-MM-dd")," -"," ",Je(l.to,"yyyy-MM-dd")]}):Je(l.from,"yyyy-MM-dd"):o("dashboard:overview.selectDate")})]})}),e.jsx(ms,{className:"w-auto p-0",align:"end",children:e.jsx(Bs,{mode:"range",defaultMonth:l?.from,selected:{from:l?.from,to:l?.to},onSelect:d=>{d?.from&&d?.to&&c({from:d.from,to:d.to})},numberOfMonths:2})})]})]}),e.jsx(Wt,{value:s,onValueChange:d=>n(d),children:e.jsxs(bt,{children:[e.jsx(Ke,{value:"amount",children:o("dashboard:overview.amount")}),e.jsx(Ke,{value:"count",children:o("dashboard:overview.count")})]})})]})]})}),e.jsxs(ss,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:o("dashboard:overview.totalIncome")}),e.jsxs("div",{className:"text-2xl font-bold",children:["¥",dt(i?.summary?.paid_total||0)]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:o("dashboard:overview.totalTransactions",{count:i?.summary?.paid_count||0})}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[o("dashboard:overview.avgOrderAmount")," ¥",dt(i?.summary?.avg_paid_amount||0)]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:o("dashboard:overview.totalCommission")}),e.jsxs("div",{className:"text-2xl font-bold",children:["¥",dt(i?.summary?.commission_total||0)]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:o("dashboard:overview.totalTransactions",{count:i?.summary?.commission_count||0})}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:[o("dashboard:overview.commissionRate")," ",i?.summary?.commission_rate.toFixed(2)||0,"%"]})]})]}),e.jsx("div",{className:"h-[400px] w-full",children:e.jsx(Gi,{width:"100%",height:"100%",children:e.jsxs(Wi,{data:i?.list||[],margin:{top:20,right:20,left:0,bottom:0},children:[e.jsxs("defs",{children:[e.jsxs("linearGradient",{id:"incomeGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"0%",stopColor:Ps.income.gradient.start,stopOpacity:.2}),e.jsx("stop",{offset:"100%",stopColor:Ps.income.gradient.end,stopOpacity:.1})]}),e.jsxs("linearGradient",{id:"commissionGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"0%",stopColor:Ps.commission.gradient.start,stopOpacity:.2}),e.jsx("stop",{offset:"100%",stopColor:Ps.commission.gradient.end,stopOpacity:.1})]})]}),e.jsx(Yi,{dataKey:"date",axisLine:!1,tickLine:!1,tick:{fill:"hsl(var(--muted-foreground))",fontSize:12},tickFormatter:d=>Je(new Date(d),"MM-dd",{locale:Xi})}),e.jsx(Qi,{axisLine:!1,tickLine:!1,tick:{fill:"hsl(var(--muted-foreground))",fontSize:12},tickFormatter:d=>s==="amount"?`¥${dt(d)}`:o("dashboard:overview.transactions",{count:d})}),e.jsx(Ji,{strokeDasharray:"3 3",vertical:!1,stroke:"hsl(var(--border))",opacity:.3}),e.jsx(Zi,{content:e.jsx(du,{})}),s==="amount"?e.jsxs(e.Fragment,{children:[e.jsx(Ga,{type:"monotone",dataKey:"paid_total",name:o("dashboard:overview.orderAmount"),stroke:Ps.income.main,fill:"url(#incomeGradient)",strokeWidth:2}),e.jsx(Ga,{type:"monotone",dataKey:"commission_total",name:o("dashboard:overview.commissionAmount"),stroke:Ps.commission.main,fill:"url(#commissionGradient)",strokeWidth:2})]}):e.jsxs(e.Fragment,{children:[e.jsx(Wa,{dataKey:"paid_count",name:o("dashboard:overview.orderCount"),fill:Ps.income.main,radius:[4,4,0,0],maxBarSize:40}),e.jsx(Wa,{dataKey:"commission_count",name:o("dashboard:overview.commissionCount"),fill:Ps.commission.main,radius:[4,4,0,0],maxBarSize:40})]})]})})})]})]})}function oe({className:s,...n}){return e.jsx("div",{className:_("animate-pulse rounded-md bg-primary/10",s),...n})}function hu(){return e.jsxs(Be,{children:[e.jsxs(es,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(oe,{className:"h-4 w-[120px]"}),e.jsx(oe,{className:"h-4 w-4"})]}),e.jsxs(ss,{children:[e.jsx(oe,{className:"h-8 w-[140px] mb-2"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(oe,{className:"h-4 w-4"}),e.jsx(oe,{className:"h-4 w-[100px]"})]})]})]})}function pu(){return e.jsx("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:Array.from({length:8}).map((s,n)=>e.jsx(hu,{},n))})}var re=(s=>(s[s.PENDING=0]="PENDING",s[s.PROCESSING=1]="PROCESSING",s[s.CANCELLED=2]="CANCELLED",s[s.COMPLETED=3]="COMPLETED",s[s.DISCOUNTED=4]="DISCOUNTED",s))(re||{});const ot={0:"待支付",1:"开通中",2:"已取消",3:"已完成",4:"已折抵"},ct={0:"yellow-500",1:"blue-500",2:"red-500",3:"green-500",4:"green-500"};var is=(s=>(s[s.NEW=1]="NEW",s[s.RENEWAL=2]="RENEWAL",s[s.UPGRADE=3]="UPGRADE",s[s.RESET_FLOW=4]="RESET_FLOW",s))(is||{}),me=(s=>(s[s.PENDING=0]="PENDING",s[s.PROCESSING=1]="PROCESSING",s[s.VALID=2]="VALID",s[s.INVALID=3]="INVALID",s))(me||{});const wt={0:"待确认",1:"发放中",2:"有效",3:"无效"},Ct={0:"yellow-500",1:"blue-500",2:"green-500",3:"red-500"};var De=(s=>(s.MONTH_PRICE="month_price",s.QUARTER_PRICE="quarter_price",s.HALF_YEAR_PRICE="half_year_price",s.YEAR_PRICE="year_price",s.TWO_YEAR_PRICE="two_year_price",s.THREE_YEAR_PRICE="three_year_price",s.ONETIME_PRICE="onetime_price",s.RESET_PRICE="reset_price",s))(De||{});const fu={month_price:"月付",quarter_price:"季付",half_year_price:"半年付",year_price:"年付",two_year_price:"两年付",three_year_price:"三年付",onetime_price:"一次性",reset_price:"流量重置包"};var Te=(s=>(s.Shadowsocks="shadowsocks",s.Vmess="vmess",s.Trojan="trojan",s.Hysteria="hysteria",s.Vless="vless",s.Tuic="tuic",s))(Te||{});const Os=[{type:"shadowsocks",label:"Shadowsocks"},{type:"vmess",label:"VMess"},{type:"trojan",label:"Trojan"},{type:"hysteria",label:"Hysteria"},{type:"vless",label:"VLess"},{type:"tuic",label:"TUIC"}],_s={shadowsocks:"#489851",vmess:"#CB3180",trojan:"#EBB749",hysteria:"#5684e6",vless:"#1a1a1a",tuic:"#00C853"};var Ue=(s=>(s[s.AMOUNT=1]="AMOUNT",s[s.PERCENTAGE=2]="PERCENTAGE",s))(Ue||{});const gu={1:"按金额优惠",2:"按比例优惠"};var Is=(s=>(s[s.OPENING=0]="OPENING",s[s.CLOSED=1]="CLOSED",s))(Is||{}),Me=(s=>(s[s.LOW=0]="LOW",s[s.MIDDLE=1]="MIDDLE",s[s.HIGH=2]="HIGH",s))(Me||{}),xt=(s=>(s.MONTH="monthly",s.QUARTER="quarterly",s.HALF_YEAR="half_yearly",s.YEAR="yearly",s.TWO_YEAR="two_yearly",s.THREE_YEAR="three_yearly",s.ONETIME="onetime",s.RESET="reset_traffic",s))(xt||{});function Es({title:s,value:n,icon:a,trend:r,description:l,onClick:c,highlight:o,className:u}){return e.jsxs(Be,{className:_("transition-colors",c&&"cursor-pointer hover:bg-muted/50",o&&"border-primary/50",u),onClick:c,children:[e.jsxs(es,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(ws,{className:"text-sm font-medium",children:s}),a]}),e.jsxs(ss,{children:[e.jsx("div",{className:"text-2xl font-bold",children:n}),r?e.jsxs("div",{className:"flex items-center pt-1",children:[e.jsx(ro,{className:_("h-4 w-4",r.isPositive?"text-emerald-500":"text-red-500")}),e.jsxs("span",{className:_("ml-1 text-xs",r.isPositive?"text-emerald-500":"text-red-500"),children:[r.isPositive?"+":"-",Math.abs(r.value),"%"]}),e.jsx("span",{className:"ml-1 text-xs text-muted-foreground",children:r.label})]}):e.jsx("p",{className:"text-xs text-muted-foreground",children:l})]})]})}function ju({className:s}){const n=ks(),{t:a}=M(),{data:r,isLoading:l}=ie({queryKey:["dashboardStats"],queryFn:async()=>(await bc()).data,refetchInterval:1e3*60*5});if(l||!r)return e.jsx(pu,{});const c=()=>{const o=new URLSearchParams;o.set("commission_status",me.PENDING.toString()),o.set("status",re.COMPLETED.toString()),o.set("commission_balance","gt:0"),n(`/finance/order?${o.toString()}`)};return e.jsxs("div",{className:_("grid gap-4 md:grid-cols-2 lg:grid-cols-4",s),children:[e.jsx(Es,{title:a("dashboard:stats.todayIncome"),value:Fs(r.todayIncome),icon:e.jsx(eo,{className:"h-4 w-4 text-emerald-500"}),trend:{value:r.dayIncomeGrowth,label:a("dashboard:stats.vsYesterday"),isPositive:r.dayIncomeGrowth>0}}),e.jsx(Es,{title:a("dashboard:stats.monthlyIncome"),value:Fs(r.currentMonthIncome),icon:e.jsx(so,{className:"h-4 w-4 text-blue-500"}),trend:{value:r.monthIncomeGrowth,label:a("dashboard:stats.vsLastMonth"),isPositive:r.monthIncomeGrowth>0}}),e.jsx(Es,{title:a("dashboard:stats.pendingTickets"),value:r.ticketPendingTotal,icon:e.jsx(to,{className:_("h-4 w-4",r.ticketPendingTotal>0?"text-orange-500":"text-muted-foreground")}),description:r.ticketPendingTotal>0?a("dashboard:stats.hasPendingTickets"):a("dashboard:stats.noPendingTickets"),onClick:()=>n("/user/ticket"),highlight:r.ticketPendingTotal>0}),e.jsx(Es,{title:a("dashboard:stats.pendingCommission"),value:r.commissionPendingTotal,icon:e.jsx(ao,{className:_("h-4 w-4",r.commissionPendingTotal>0?"text-blue-500":"text-muted-foreground")}),description:r.commissionPendingTotal>0?a("dashboard:stats.hasPendingCommission"):a("dashboard:stats.noPendingCommission"),onClick:c,highlight:r.commissionPendingTotal>0}),e.jsx(Es,{title:a("dashboard:stats.monthlyNewUsers"),value:r.currentMonthNewUsers,icon:e.jsx(pa,{className:"h-4 w-4 text-blue-500"}),trend:{value:r.userGrowth,label:a("dashboard:stats.vsLastMonth"),isPositive:r.userGrowth>0}}),e.jsx(Es,{title:a("dashboard:stats.totalUsers"),value:r.totalUsers,icon:e.jsx(pa,{className:"h-4 w-4 text-muted-foreground"}),description:a("dashboard:stats.activeUsers",{count:r.activeUsers})}),e.jsx(Es,{title:a("dashboard:stats.monthlyUpload"),value:hs(r.monthTraffic.upload),icon:e.jsx(pt,{className:"h-4 w-4 text-emerald-500"}),description:a("dashboard:stats.todayTraffic",{value:hs(r.todayTraffic.upload)})}),e.jsx(Es,{title:a("dashboard:stats.monthlyDownload"),value:hs(r.monthTraffic.download),icon:e.jsx(no,{className:"h-4 w-4 text-blue-500"}),description:a("dashboard:stats.todayTraffic",{value:hs(r.todayTraffic.download)})})]})}const Na={today:{getValue:()=>{const s=io();return{start:s,end:oo(s,1)}}},last7days:{getValue:()=>{const s=new Date;return{start:ls(s,7),end:s}}},last30days:{getValue:()=>{const s=new Date;return{start:ls(s,30),end:s}}},custom:{getValue:()=>null}};function ln({selectedRange:s,customDateRange:n,onRangeChange:a,onCustomRangeChange:r}){const{t:l}=M(),c={today:l("dashboard:trafficRank.today"),last7days:l("dashboard:trafficRank.last7days"),last30days:l("dashboard:trafficRank.last30days"),custom:l("dashboard:trafficRank.customRange")};return e.jsxs("div",{className:"flex min-w-0 flex-wrap items-center gap-1",children:[e.jsxs(J,{value:s,onValueChange:a,children:[e.jsx(W,{className:"w-[120px]",children:e.jsx(Z,{placeholder:l("dashboard:trafficRank.selectTimeRange")})}),e.jsx(Y,{position:"popper",className:"z-50",children:Object.entries(Na).map(([o])=>e.jsx(q,{value:o,children:c[o]},o))})]}),s==="custom"&&e.jsxs(js,{children:[e.jsx(vs,{asChild:!0,children:e.jsxs(X,{variant:"outline",className:_("min-w-0 justify-start text-left font-normal",!n&&"text-muted-foreground"),children:[e.jsx(jt,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:n?.from?n.to?e.jsxs(e.Fragment,{children:[Je(n.from,"yyyy-MM-dd")," -"," ",Je(n.to,"yyyy-MM-dd")]}):Je(n.from,"yyyy-MM-dd"):e.jsx("span",{children:l("dashboard:trafficRank.selectDateRange")})})]})}),e.jsx(ms,{className:"w-auto p-0",align:"end",children:e.jsx(Bs,{mode:"range",defaultMonth:n?.from,selected:{from:n?.from,to:n?.to},onSelect:o=>{o?.from&&o?.to&&r({from:o.from,to:o.to})},numberOfMonths:2})})]})]})}const Ws=s=>`${(s/1024/1024/1024).toFixed(2)} GB`;function vu({className:s}){const{t:n}=M(),[a,r]=m.useState("today"),[l,c]=m.useState({from:ls(new Date,7),to:new Date}),[o,u]=m.useState("today"),[x,i]=m.useState({from:ls(new Date,7),to:new Date}),d=m.useMemo(()=>a==="custom"?{start:l.from,end:l.to}:Na[a].getValue(),[a,l]),f=m.useMemo(()=>o==="custom"?{start:x.from,end:x.to}:Na[o].getValue(),[o,x]),{data:w}=ie({queryKey:["nodeTrafficRank",d.start,d.end],queryFn:()=>tn({type:"node",start_time:Ce.round(d.start.getTime()/1e3),end_time:Ce.round(d.end.getTime()/1e3)}),refetchInterval:3e4}),{data:P}=ie({queryKey:["userTrafficRank",f.start,f.end],queryFn:()=>tn({type:"user",start_time:Ce.round(f.start.getTime()/1e3),end_time:Ce.round(f.end.getTime()/1e3)}),refetchInterval:3e4});return e.jsxs("div",{className:_("grid gap-4 md:grid-cols-2",s),children:[e.jsxs(Be,{children:[e.jsx(es,{className:"flex-none pb-2",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[e.jsxs(ws,{className:"flex items-center text-base font-medium",children:[e.jsx(lo,{className:"mr-2 h-4 w-4"}),n("dashboard:trafficRank.nodeTrafficRank")]}),e.jsxs("div",{className:"flex min-w-0 items-center gap-1",children:[e.jsx(ln,{selectedRange:a,customDateRange:l,onRangeChange:r,onCustomRangeChange:c}),e.jsx(Ya,{className:"h-4 w-4 flex-shrink-0 text-muted-foreground"})]})]})}),e.jsx(ss,{className:"flex-1",children:w?.data?e.jsxs(zs,{className:"h-[400px] pr-4",children:[e.jsx("div",{className:"space-y-3",children:w.data.map(C=>e.jsx(je,{delayDuration:200,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx("div",{className:"flex cursor-pointer items-center justify-between space-x-2 rounded-lg bg-muted/50 p-2 transition-colors hover:bg-muted/70",children:e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"truncate text-sm font-medium",children:C.name}),e.jsxs("span",{className:_("ml-2 flex items-center text-xs font-medium",C.change>=0?"text-green-600":"text-red-600"),children:[C.change>=0?e.jsx(fa,{className:"mr-1 h-3 w-3"}):e.jsx(ga,{className:"mr-1 h-3 w-3"}),Math.abs(C.change),"%"]})]}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx("div",{className:"h-2 flex-1 overflow-hidden rounded-full bg-muted",children:e.jsx("div",{className:"h-full bg-primary transition-all",style:{width:`${C.value/w.data[0].value*100}%`}})}),e.jsx("span",{className:"text-xs text-muted-foreground",children:Ws(C.value)})]})]})})}),e.jsx(ce,{side:"right",className:"space-y-2 p-4",children:e.jsxs("div",{className:"grid grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[e.jsxs("span",{className:"text-muted-foreground",children:[n("dashboard:trafficRank.currentTraffic"),"："]}),e.jsx("span",{className:"font-medium",children:Ws(C.value)}),e.jsxs("span",{className:"text-muted-foreground",children:[n("dashboard:trafficRank.previousTraffic"),"："]}),e.jsx("span",{className:"font-medium",children:Ws(C.previousValue)}),e.jsxs("span",{className:"text-muted-foreground",children:[n("dashboard:trafficRank.changeRate"),"："]}),e.jsxs("span",{className:_("font-medium",C.change>=0?"text-green-600":"text-red-600"),children:[C.change>=0?"+":"",C.change,"%"]})]})})]})},C.id))}),e.jsx(At,{orientation:"vertical"})]}):e.jsx("div",{className:"flex h-[400px] items-center justify-center",children:e.jsx("div",{className:"animate-pulse",children:n("common:loading")})})})]}),e.jsxs(Be,{children:[e.jsx(es,{className:"flex-none pb-2",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[e.jsxs(ws,{className:"flex items-center text-base font-medium",children:[e.jsx(pa,{className:"mr-2 h-4 w-4"}),n("dashboard:trafficRank.userTrafficRank")]}),e.jsxs("div",{className:"flex min-w-0 items-center gap-1",children:[e.jsx(ln,{selectedRange:o,customDateRange:x,onRangeChange:u,onCustomRangeChange:i}),e.jsx(Ya,{className:"h-4 w-4 flex-shrink-0 text-muted-foreground"})]})]})}),e.jsx(ss,{className:"flex-1",children:P?.data?e.jsxs(zs,{className:"h-[400px] pr-4",children:[e.jsx("div",{className:"space-y-3",children:P.data.map(C=>e.jsx(je,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx("div",{className:"flex cursor-pointer items-center justify-between space-x-2 rounded-lg bg-muted/50 p-2 transition-colors hover:bg-muted/70",children:e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"truncate text-sm font-medium",children:C.name}),e.jsxs("span",{className:_("ml-2 flex items-center text-xs font-medium",C.change>=0?"text-green-600":"text-red-600"),children:[C.change>=0?e.jsx(fa,{className:"mr-1 h-3 w-3"}):e.jsx(ga,{className:"mr-1 h-3 w-3"}),Math.abs(C.change),"%"]})]}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx("div",{className:"h-2 flex-1 overflow-hidden rounded-full bg-muted",children:e.jsx("div",{className:"h-full bg-primary transition-all",style:{width:`${C.value/P.data[0].value*100}%`}})}),e.jsx("span",{className:"text-xs text-muted-foreground",children:Ws(C.value)})]})]})})}),e.jsx(ce,{side:"right",className:"space-y-2 p-4",children:e.jsxs("div",{className:"grid grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[e.jsxs("span",{className:"text-muted-foreground",children:[n("dashboard:trafficRank.currentTraffic"),"："]}),e.jsx("span",{className:"font-medium",children:Ws(C.value)}),e.jsxs("span",{className:"text-muted-foreground",children:[n("dashboard:trafficRank.previousTraffic"),"："]}),e.jsx("span",{className:"font-medium",children:Ws(C.previousValue)}),e.jsxs("span",{className:"text-muted-foreground",children:[n("dashboard:trafficRank.changeRate"),"："]}),e.jsxs("span",{className:_("font-medium",C.change>=0?"text-green-600":"text-red-600"),children:[C.change>=0?"+":"",C.change,"%"]})]})})]})},C.id))}),e.jsx(At,{orientation:"vertical"})]}):e.jsx("div",{className:"flex h-[400px] items-center justify-center",children:e.jsx("div",{className:"animate-pulse",children:n("common:loading")})})})]})]})}const Et=m.forwardRef(({className:s,value:n,...a},r)=>e.jsx(sr,{ref:r,className:_("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",s),...a,children:e.jsx(co,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(n||0)}%)`}})}));Et.displayName=sr.displayName;function bu(){const{t:s}=M(),[n,a]=m.useState(null),[r,l]=m.useState(null),[c,o]=m.useState(!0),[u,x]=m.useState(!1),i=async()=>{try{x(!0);const[w,P]=await Promise.all([rn.getSystemStatus(),rn.getQueueStats()]);a(w.data),l(P.data)}catch(w){console.error("Error fetching system data:",w)}finally{o(!1),x(!1)}};m.useEffect(()=>{i();const w=setInterval(i,3e4);return()=>clearInterval(w)},[]);const d=()=>{i()};if(c)return e.jsx("div",{className:"flex items-center justify-center p-6",children:e.jsx(Pa,{className:"h-6 w-6 animate-spin"})});const f=w=>w?e.jsx(tr,{className:"h-5 w-5 text-green-500"}):e.jsx(ar,{className:"h-5 w-5 text-red-500"});return e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs(Be,{children:[e.jsxs(es,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs(ws,{className:"flex items-center gap-2",children:[e.jsx(uo,{className:"h-5 w-5"}),s("dashboard:queue.title")]}),e.jsx(Zs,{children:s("dashboard:queue.status.description")})]}),e.jsx(X,{variant:"outline",size:"icon",onClick:d,disabled:u,children:e.jsx(mo,{className:_("h-4 w-4",u&&"animate-spin")})})]}),e.jsx(ss,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2 rounded-lg bg-muted/50 p-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[f(r?.status||!1),e.jsx("span",{className:"font-medium",children:s("dashboard:queue.status.running")})]}),e.jsx(K,{variant:r?.status?"secondary":"destructive",children:r?.status?s("dashboard:queue.status.normal"):s("dashboard:queue.status.abnormal")})]}),e.jsx("div",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.status.waitTime",{seconds:r?.wait?.default||0})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(je,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsxs("div",{className:"space-y-2 rounded-lg bg-muted/50 p-3",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.details.recentJobs")}),e.jsx("p",{className:"text-2xl font-bold",children:r?.recentJobs||0}),e.jsx(Et,{value:(r?.recentJobs||0)/(r?.periods?.recentJobs||1)*100,className:"h-1"})]})}),e.jsx(ce,{children:e.jsx("p",{children:s("dashboard:queue.details.statisticsPeriod",{hours:r?.periods?.recentJobs||0})})})]})}),e.jsx(je,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsxs("div",{className:"space-y-2 rounded-lg bg-muted/50 p-3",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.details.jobsPerMinute")}),e.jsx("p",{className:"text-2xl font-bold",children:r?.jobsPerMinute||0}),e.jsx(Et,{value:(r?.jobsPerMinute||0)/(r?.queueWithMaxThroughput?.throughput||1)*100,className:"h-1"})]})}),e.jsx(ce,{children:e.jsx("p",{children:s("dashboard:queue.details.maxThroughput",{value:r?.queueWithMaxThroughput?.throughput||0})})})]})})]})]})})]}),e.jsxs(Be,{children:[e.jsxs(es,{children:[e.jsxs(ws,{className:"flex items-center gap-2",children:[e.jsx(xo,{className:"h-5 w-5"}),s("dashboard:queue.jobDetails")]}),e.jsx(Zs,{children:s("dashboard:queue.details.description")})]}),e.jsx(ss,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2 rounded-lg bg-muted/50 p-3",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.details.failedJobs7Days")}),e.jsx("p",{className:"text-2xl font-bold text-destructive",children:r?.failedJobs||0}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s("dashboard:queue.details.retentionPeriod",{hours:r?.periods?.failedJobs||0})})]}),e.jsxs("div",{className:"space-y-2 rounded-lg bg-muted/50 p-3",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.details.longestRunningQueue")}),e.jsxs("p",{className:"text-2xl font-bold",children:[r?.queueWithMaxRuntime?.runtime||0,"s"]}),e.jsx("div",{className:"truncate text-xs text-muted-foreground",children:r?.queueWithMaxRuntime?.name||"N/A"})]})]}),e.jsxs("div",{className:"rounded-lg bg-muted/50 p-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:s("dashboard:queue.details.activeProcesses")}),e.jsxs("span",{className:"font-medium",children:[r?.processes||0," /"," ",(r?.processes||0)+(r?.pausedMasters||0)]})]}),e.jsx(Et,{value:(r?.processes||0)/((r?.processes||0)+(r?.pausedMasters||0))*100,className:"mt-2 h-1"})]})]})})]})]})}function yu(){const{t:s}=M();return e.jsxs(Pe,{children:[e.jsxs(Ee,{children:[e.jsx("div",{className:"flex items-center",children:e.jsx("h1",{className:"text-2xl font-bold tracking-tight md:text-3xl",children:s("dashboard:title")})}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(qe,{}),e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsx(Ve,{children:e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"grid gap-6",children:[e.jsx(ju,{}),e.jsx(xu,{}),e.jsx(vu,{}),e.jsx(bu,{})]})})})]})}const Nu=Object.freeze(Object.defineProperty({__proto__:null,default:yu},Symbol.toStringTag,{value:"Module"}));function _u({className:s,items:n,...a}){const{pathname:r}=wa(),l=ks(),[c,o]=m.useState(r??"/settings"),u=i=>{o(i),l(i)},{t:x}=M("settings");return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"p-1 md:hidden",children:e.jsxs(J,{value:c,onValueChange:u,children:[e.jsx(W,{className:"h-12 sm:w-48",children:e.jsx(Z,{placeholder:"Theme"})}),e.jsx(Y,{children:n.map(i=>e.jsx(q,{value:i.href,children:e.jsxs("div",{className:"flex gap-x-4 px-2 py-1",children:[e.jsx("span",{className:"scale-125",children:i.icon}),e.jsx("span",{className:"text-md",children:x(i.title)})]})},i.href))})]})}),e.jsx("div",{className:"hidden w-full overflow-x-auto bg-background px-1 py-2 md:block",children:e.jsx("nav",{className:_("flex space-x-2 lg:flex-col lg:space-x-0 lg:space-y-1",s),...a,children:n.map(i=>e.jsxs($s,{to:i.href,className:_(tt({variant:"ghost"}),r===i.href?"bg-muted hover:bg-muted":"hover:bg-transparent hover:underline","justify-start"),children:[e.jsx("span",{className:"mr-2",children:i.icon}),x(i.title)]},i.href))})})]})}const wu=[{title:"site.title",key:"site",icon:e.jsx(ho,{size:18}),href:"/config/system",description:"site.description"},{title:"safe.title",key:"safe",icon:e.jsx(zn,{size:18}),href:"/config/system/safe",description:"safe.description"},{title:"subscribe.title",key:"subscribe",icon:e.jsx(Ln,{size:18}),href:"/config/system/subscribe",description:"subscribe.description"},{title:"invite.title",key:"invite",icon:e.jsx(po,{size:18}),href:"/config/system/invite",description:"invite.description"},{title:"server.title",key:"server",icon:e.jsx(On,{size:18}),href:"/config/system/server",description:"server.description"},{title:"email.title",key:"email",icon:e.jsx(fo,{size:18}),href:"/config/system/email",description:"email.description"},{title:"telegram.title",key:"telegram",icon:e.jsx(go,{size:18}),href:"/config/system/telegram",description:"telegram.description"},{title:"app.title",key:"app",icon:e.jsx(Fn,{size:18}),href:"/config/system/app",description:"app.description"},{title:"subscribe_template.title",key:"subscribe_template",icon:e.jsx(jo,{size:18}),href:"/config/system/subscribe-template",description:"subscribe_template.description"}];function Cu(){const{t:s}=M("settings");return e.jsxs(Pe,{fadedBelow:!0,fixedHeight:!0,children:[e.jsxs(Ee,{children:[e.jsx(qe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h1",{className:"text-2xl font-bold tracking-tight md:text-3xl",children:s("title")}),e.jsx("div",{className:"text-muted-foreground",children:s("description")})]}),e.jsx(Se,{className:"my-6"}),e.jsxs("div",{className:"flex flex-1 flex-col space-y-8 overflow-auto lg:flex-row lg:space-x-12 lg:space-y-0",children:[e.jsx("aside",{className:"sticky top-0 lg:w-1/5",children:e.jsx(_u,{items:wu})}),e.jsx("div",{className:"flex-1 w-full p-1 pr-4",children:e.jsx("div",{className:"pb-16",children:e.jsx(Ca,{})})})]})]})]})}const Su=Object.freeze(Object.defineProperty({__proto__:null,default:Cu},Symbol.toStringTag,{value:"Module"})),Q=m.forwardRef(({className:s,...n},a)=>e.jsx(nr,{className:_("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...n,ref:a,children:e.jsx(vo,{className:_("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})}));Q.displayName=nr.displayName;const bs=m.forwardRef(({className:s,...n},a)=>e.jsx("textarea",{className:_("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",s),ref:a,...n}));bs.displayName="Textarea";const ku=h.object({logo:h.string().nullable().default(""),force_https:h.number().nullable().default(0),stop_register:h.number().nullable().default(0),app_name:h.string().nullable().default(""),app_description:h.string().nullable().default(""),app_url:h.string().nullable().default(""),subscribe_url:h.string().nullable().default(""),try_out_plan_id:h.number().nullable().default(0),try_out_hour:h.coerce.number().nullable().default(0),tos_url:h.string().nullable().default(""),currency:h.string().nullable().default(""),currency_symbol:h.string().nullable().default("")});function Tu(){const{t:s}=M("settings"),[n,a]=m.useState(!1),r=m.useRef(null),{data:l}=ie({queryKey:["settings","site"],queryFn:()=>fs("site")}),{data:c}=ie({queryKey:["plans"],queryFn:()=>Hs()}),o=fe({resolver:ye(ku),defaultValues:{},mode:"onBlur"}),{mutateAsync:u}=Ze({mutationFn:gs,onSuccess:d=>{d.data&&A.success(s("common.autoSaved"))}});m.useEffect(()=>{if(l?.data?.site){const d=l?.data?.site;Object.entries(d).forEach(([f,w])=>{o.setValue(f,w)}),r.current=d}},[l]);const x=m.useCallback(Ce.debounce(async d=>{if(!Ce.isEqual(d,r.current)){a(!0);try{const f=Object.entries(d).reduce((w,[P,C])=>(w[P]=C===null?"":C,w),{});await u(f),r.current=d}finally{a(!1)}}},1e3),[u]),i=m.useCallback(d=>{x(d)},[x]);return m.useEffect(()=>{const d=o.watch(f=>{i(f)});return()=>d.unsubscribe()},[o.watch,i]),e.jsx(Ne,{...o,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{control:o.control,name:"app_name",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("site.form.siteName.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("site.form.siteName.placeholder"),...d,value:d.value||"",onChange:f=>{d.onChange(f),i(o.getValues())}})}),e.jsx(z,{children:s("site.form.siteName.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:o.control,name:"app_description",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("site.form.siteDescription.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("site.form.siteDescription.placeholder"),...d,value:d.value||"",onChange:f=>{d.onChange(f),i(o.getValues())}})}),e.jsx(z,{children:s("site.form.siteDescription.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:o.control,name:"app_url",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("site.form.siteUrl.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("site.form.siteUrl.placeholder"),...d,value:d.value||"",onChange:f=>{d.onChange(f),i(o.getValues())}})}),e.jsx(z,{children:s("site.form.siteUrl.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:o.control,name:"force_https",render:({field:d})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("site.form.forceHttps.label")}),e.jsx(z,{children:s("site.form.forceHttps.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:!!d.value,onCheckedChange:f=>{d.onChange(Number(f)),i(o.getValues())}})})]})}),e.jsx(b,{control:o.control,name:"logo",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("site.form.logo.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("site.form.logo.placeholder"),...d,value:d.value||"",onChange:f=>{d.onChange(f),i(o.getValues())}})}),e.jsx(z,{children:s("site.form.logo.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:o.control,name:"subscribe_url",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("site.form.subscribeUrl.label")}),e.jsx(N,{children:e.jsx(bs,{placeholder:s("site.form.subscribeUrl.placeholder"),...d,value:d.value||"",onChange:f=>{d.onChange(f),i(o.getValues())}})}),e.jsx(z,{children:s("site.form.subscribeUrl.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:o.control,name:"tos_url",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("site.form.tosUrl.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("site.form.tosUrl.placeholder"),...d,value:d.value||"",onChange:f=>{d.onChange(f),i(o.getValues())}})}),e.jsx(z,{children:s("site.form.tosUrl.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:o.control,name:"stop_register",render:({field:d})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("site.form.stopRegister.label")}),e.jsx(z,{children:s("site.form.stopRegister.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:!!d.value,onCheckedChange:f=>{d.onChange(Number(f)),i(o.getValues())}})})]})}),e.jsx(b,{control:o.control,name:"try_out_plan_id",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("site.form.tryOut.label")}),e.jsx(N,{children:e.jsxs(J,{value:d.value?.toString(),onValueChange:f=>{d.onChange(Number(f)),i(o.getValues())},children:[e.jsx(W,{children:e.jsx(Z,{placeholder:s("site.form.tryOut.placeholder")})}),e.jsxs(Y,{children:[e.jsx(q,{value:"0",children:s("site.form.tryOut.placeholder")}),c?.data?.map(f=>e.jsx(q,{value:f.id.toString(),children:f.name},f.id.toString()))]})]})}),e.jsx(z,{children:s("site.form.tryOut.description")}),e.jsx(k,{})]})}),!!o.watch("try_out_plan_id")&&e.jsx(b,{control:o.control,name:"try_out_hour",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"",children:s("site.form.tryOut.duration.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("site.form.tryOut.duration.placeholder"),...d,value:d.value||"",onChange:f=>{d.onChange(f),i(o.getValues())}})}),e.jsx(z,{children:s("site.form.tryOut.duration.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:o.control,name:"currency",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("site.form.currency.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("site.form.currency.placeholder"),...d,value:d.value||"",onChange:f=>{d.onChange(f),i(o.getValues())}})}),e.jsx(z,{children:s("site.form.currency.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:o.control,name:"currency_symbol",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("site.form.currencySymbol.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("site.form.currencySymbol.placeholder"),...d,value:d.value||"",onChange:f=>{d.onChange(f),i(o.getValues())}})}),e.jsx(z,{children:s("site.form.currencySymbol.description")}),e.jsx(k,{})]})}),n&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("site.form.saving")})]})})}function Du(){const{t:s}=M("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("site.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("site.description")})]}),e.jsx(Se,{}),e.jsx(Tu,{})]})}const Pu=Object.freeze(Object.defineProperty({__proto__:null,default:Du},Symbol.toStringTag,{value:"Module"})),Eu=h.object({email_verify:h.boolean().nullable(),safe_mode_enable:h.boolean().nullable(),secure_path:h.string().nullable(),email_whitelist_enable:h.boolean().nullable(),email_whitelist_suffix:h.array(h.string().nullable()).nullable(),email_gmail_limit_enable:h.boolean().nullable(),recaptcha_enable:h.boolean().nullable(),recaptcha_key:h.string().nullable(),recaptcha_site_key:h.string().nullable(),register_limit_by_ip_enable:h.boolean().nullable(),register_limit_count:h.coerce.string().transform(s=>s===""?null:s).nullable(),register_limit_expire:h.coerce.string().transform(s=>s===""?null:s).nullable(),password_limit_enable:h.boolean().nullable(),password_limit_count:h.coerce.string().transform(s=>s===""?null:s).nullable(),password_limit_expire:h.coerce.string().transform(s=>s===""?null:s).nullable()}),Ru={email_verify:!1,safe_mode_enable:!1,secure_path:"",email_whitelist_enable:!1,email_whitelist_suffix:[],email_gmail_limit_enable:!1,recaptcha_enable:!1,recaptcha_key:"",recaptcha_site_key:"",register_limit_by_ip_enable:!1,register_limit_count:"",register_limit_expire:"",password_limit_enable:!1,password_limit_count:"",password_limit_expire:""};function Iu(){const{t:s}=M("settings"),[n,a]=m.useState(!1),r=m.useRef(null),l=fe({resolver:ye(Eu),defaultValues:Ru,mode:"onBlur"}),{data:c}=ie({queryKey:["settings","safe"],queryFn:()=>fs("safe")}),{mutateAsync:o}=Ze({mutationFn:gs,onSuccess:i=>{i.data&&A.success(s("common.autoSaved"))}});m.useEffect(()=>{if(c?.data.safe){const i=c.data.safe;Object.entries(i).forEach(([d,f])=>{typeof f=="number"?l.setValue(d,String(f)):l.setValue(d,f)}),r.current=i}},[c]);const u=m.useCallback(Ce.debounce(async i=>{if(!Ce.isEqual(i,r.current)){a(!0);try{await o(i),r.current=i}finally{a(!1)}}},1e3),[o]),x=m.useCallback(i=>{u(i)},[u]);return m.useEffect(()=>{const i=l.watch(d=>{x(d)});return()=>i.unsubscribe()},[l.watch,x]),e.jsx(Ne,{...l,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{control:l.control,name:"email_verify",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("safe.form.emailVerify.label")}),e.jsx(z,{children:s("safe.form.emailVerify.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),e.jsx(b,{control:l.control,name:"email_gmail_limit_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("safe.form.gmailLimit.label")}),e.jsx(z,{children:s("safe.form.gmailLimit.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),e.jsx(b,{control:l.control,name:"safe_mode_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("safe.form.safeMode.label")}),e.jsx(z,{children:s("safe.form.safeMode.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),e.jsx(b,{control:l.control,name:"secure_path",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("safe.form.securePath.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("safe.form.securePath.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),x(l.getValues())}})}),e.jsx(z,{children:s("safe.form.securePath.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"email_whitelist_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("safe.form.emailWhitelist.label")}),e.jsx(z,{children:s("safe.form.emailWhitelist.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),l.watch("email_whitelist_enable")&&e.jsx(b,{control:l.control,name:"email_whitelist_suffix",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("safe.form.emailWhitelist.suffixes.label")}),e.jsx(N,{children:e.jsx(bs,{placeholder:s("safe.form.emailWhitelist.suffixes.placeholder"),...i,value:(i.value||[]).join(`
`),onChange:d=>{const f=d.target.value.split(`
`).filter(Boolean);i.onChange(f),x(l.getValues())}})}),e.jsx(z,{children:s("safe.form.emailWhitelist.suffixes.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"recaptcha_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("safe.form.recaptcha.enable.label")}),e.jsx(z,{children:s("safe.form.recaptcha.enable.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),l.watch("recaptcha_enable")&&e.jsxs(e.Fragment,{children:[e.jsx(b,{control:l.control,name:"recaptcha_site_key",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("safe.form.recaptcha.siteKey.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("safe.form.recaptcha.siteKey.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),x(l.getValues())}})}),e.jsx(z,{children:s("safe.form.recaptcha.siteKey.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"recaptcha_key",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("safe.form.recaptcha.key.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("safe.form.recaptcha.key.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),x(l.getValues())}})}),e.jsx(z,{children:s("safe.form.recaptcha.key.description")}),e.jsx(k,{})]})})]}),e.jsx(b,{control:l.control,name:"register_limit_by_ip_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("safe.form.registerLimit.enable.label")}),e.jsx(z,{children:s("safe.form.registerLimit.enable.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),l.watch("register_limit_by_ip_enable")&&e.jsxs(e.Fragment,{children:[e.jsx(b,{control:l.control,name:"register_limit_count",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("safe.form.registerLimit.count.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("safe.form.registerLimit.count.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),x(l.getValues())}})}),e.jsx(z,{children:s("safe.form.registerLimit.count.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"register_limit_expire",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("safe.form.registerLimit.expire.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("safe.form.registerLimit.expire.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),x(l.getValues())}})}),e.jsx(z,{children:s("safe.form.registerLimit.expire.description")}),e.jsx(k,{})]})})]}),e.jsx(b,{control:l.control,name:"password_limit_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("safe.form.passwordLimit.enable.label")}),e.jsx(z,{children:s("safe.form.passwordLimit.enable.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),l.watch("password_limit_enable")&&e.jsxs(e.Fragment,{children:[e.jsx(b,{control:l.control,name:"password_limit_count",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("safe.form.passwordLimit.count.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("safe.form.passwordLimit.count.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),x(l.getValues())}})}),e.jsx(z,{children:s("safe.form.passwordLimit.count.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"password_limit_expire",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("safe.form.passwordLimit.expire.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("safe.form.passwordLimit.expire.placeholder"),...i,value:i.value||"",onChange:d=>{i.onChange(d),x(l.getValues())}})}),e.jsx(z,{children:s("safe.form.passwordLimit.expire.description")}),e.jsx(k,{})]})})]}),n&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("safe.form.saving")})]})})}function Vu(){const{t:s}=M("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("safe.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("safe.description")})]}),e.jsx(Se,{}),e.jsx(Iu,{})]})}const Mu=Object.freeze(Object.defineProperty({__proto__:null,default:Vu},Symbol.toStringTag,{value:"Module"})),Fu=h.object({plan_change_enable:h.boolean().nullable().default(!1),reset_traffic_method:h.coerce.number().nullable().default(0),surplus_enable:h.boolean().nullable().default(!1),new_order_event_id:h.coerce.number().nullable().default(0),renew_order_event_id:h.coerce.number().nullable().default(0),change_order_event_id:h.coerce.number().nullable().default(0),show_info_to_server_enable:h.boolean().nullable().default(!1),show_protocol_to_server_enable:h.boolean().nullable().default(!1),default_remind_expire:h.boolean().nullable().default(!1),default_remind_traffic:h.boolean().nullable().default(!1),subscribe_path:h.string().nullable().default("s")}),Ou={plan_change_enable:!1,reset_traffic_method:0,surplus_enable:!1,new_order_event_id:0,renew_order_event_id:0,change_order_event_id:0,show_info_to_server_enable:!1,show_protocol_to_server_enable:!1,default_remind_expire:!1,default_remind_traffic:!1,subscribe_path:"s"};function zu(){const{t:s}=M("settings"),[n,a]=m.useState(!1),r=m.useRef(null),l=fe({resolver:ye(Fu),defaultValues:Ou,mode:"onBlur"}),{data:c}=ie({queryKey:["settings","subscribe"],queryFn:()=>fs("subscribe")}),{mutateAsync:o}=Ze({mutationFn:gs,onSuccess:i=>{i.data&&A.success(s("common.autoSaved"))}});m.useEffect(()=>{if(c?.data?.subscribe){const i=c?.data?.subscribe;Object.entries(i).forEach(([d,f])=>{l.setValue(d,f)}),r.current=i}},[c]);const u=m.useCallback(Ce.debounce(async i=>{if(!Ce.isEqual(i,r.current)){a(!0);try{await o(i),r.current=i}finally{a(!1)}}},1e3),[o]),x=m.useCallback(i=>{u(i)},[u]);return m.useEffect(()=>{const i=l.watch(d=>{x(d)});return()=>i.unsubscribe()},[l.watch,x]),e.jsx(Ne,{...l,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{control:l.control,name:"plan_change_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe.plan_change_enable.title")}),e.jsx(z,{children:s("subscribe.plan_change_enable.description")}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"reset_traffic_method",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe.reset_traffic_method.title")}),e.jsxs(J,{onValueChange:i.onChange,value:i.value?.toString()||"0",children:[e.jsx(N,{children:e.jsx(W,{children:e.jsx(Z,{placeholder:"请选择重置方式"})})}),e.jsxs(Y,{children:[e.jsx(q,{value:"0",children:s("subscribe.reset_traffic_method.options.monthly_first")}),e.jsx(q,{value:"1",children:s("subscribe.reset_traffic_method.options.monthly_reset")}),e.jsx(q,{value:"2",children:s("subscribe.reset_traffic_method.options.no_reset")}),e.jsx(q,{value:"3",children:s("subscribe.reset_traffic_method.options.yearly_first")}),e.jsx(q,{value:"4",children:s("subscribe.reset_traffic_method.options.yearly_reset")})]})]}),e.jsx(z,{children:s("subscribe.reset_traffic_method.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"surplus_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe.surplus_enable.title")}),e.jsx(z,{children:s("subscribe.surplus_enable.description")}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"new_order_event_id",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe.new_order_event.title")}),e.jsx("div",{className:"relative w-max",children:e.jsx(N,{children:e.jsxs(J,{onValueChange:i.onChange,value:i.value?.toString(),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:"请选择"})}),e.jsxs(Y,{children:[e.jsx(q,{value:"0",children:s("subscribe.new_order_event.options.no_action")}),e.jsx(q,{value:"1",children:s("subscribe.new_order_event.options.reset_traffic")})]})]})})}),e.jsx(z,{children:s("subscribe.new_order_event.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"renew_order_event_id",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe.renew_order_event.title")}),e.jsx("div",{className:"relative w-max",children:e.jsx(N,{children:e.jsxs(J,{onValueChange:i.onChange,value:i.value?.toString(),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:"请选择"})}),e.jsxs(Y,{children:[e.jsx(q,{value:"0",children:s("subscribe.renew_order_event.options.no_action")}),e.jsx(q,{value:"1",children:s("subscribe.renew_order_event.options.reset_traffic")})]})]})})}),e.jsx(z,{children:s("renew_order_event.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"change_order_event_id",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe.change_order_event.title")}),e.jsx("div",{className:"relative w-max",children:e.jsx(N,{children:e.jsxs(J,{onValueChange:i.onChange,value:i.value?.toString(),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:"请选择"})}),e.jsxs(Y,{children:[e.jsx(q,{value:"0",children:s("subscribe.change_order_event.options.no_action")}),e.jsx(q,{value:"1",children:s("subscribe.change_order_event.options.reset_traffic")})]})]})})}),e.jsx(z,{children:s("subscribe.change_order_event.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"subscribe_path",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("subscribe.subscribe_path.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:"subscribe",...i,value:i.value||"",onChange:d=>{i.onChange(d),x(l.getValues())}})}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[s("subscribe.subscribe_path.description"),e.jsx("br",{}),s("subscribe.subscribe_path.current_format",{path:i.value||"s"})]}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"show_info_to_server_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("subscribe.show_info_to_server.title")}),e.jsx(z,{children:s("subscribe.show_info_to_server.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),e.jsx(b,{control:l.control,name:"show_protocol_to_server_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("subscribe.show_protocol_to_server.title")}),e.jsx(z,{children:s("subscribe.show_protocol_to_server.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value||!1,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),n&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("common.saving")})]})})}function Lu(){const{t:s}=M("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("subscribe.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("subscribe.description")})]}),e.jsx(Se,{}),e.jsx(zu,{})]})}const Au=Object.freeze(Object.defineProperty({__proto__:null,default:Lu},Symbol.toStringTag,{value:"Module"})),$u=h.object({invite_force:h.boolean().default(!1),invite_commission:h.coerce.string().default("0"),invite_gen_limit:h.coerce.string().default("0"),invite_never_expire:h.boolean().default(!1),commission_first_time_enable:h.boolean().default(!1),commission_auto_check_enable:h.boolean().default(!1),commission_withdraw_limit:h.coerce.string().default("0"),commission_withdraw_method:h.array(h.string()).default(["支付宝","USDT","Paypal"]),withdraw_close_enable:h.boolean().default(!1),commission_distribution_enable:h.boolean().default(!1),commission_distribution_l1:h.coerce.number().default(0),commission_distribution_l2:h.coerce.number().default(0),commission_distribution_l3:h.coerce.number().default(0)}),qu={invite_force:!1,invite_commission:"0",invite_gen_limit:"0",invite_never_expire:!1,commission_first_time_enable:!1,commission_auto_check_enable:!1,commission_withdraw_limit:"0",commission_withdraw_method:["支付宝","USDT","Paypal"],withdraw_close_enable:!1,commission_distribution_enable:!1,commission_distribution_l1:0,commission_distribution_l2:0,commission_distribution_l3:0};function Hu(){const{t:s}=M("settings"),[n,a]=m.useState(!1),r=m.useRef(null),l=fe({resolver:ye($u),defaultValues:qu,mode:"onBlur"}),{data:c}=ie({queryKey:["settings","invite"],queryFn:()=>fs("invite")}),{mutateAsync:o}=Ze({mutationFn:gs,onSuccess:i=>{i.data&&A.success(s("common.autoSaved"))}});m.useEffect(()=>{if(c?.data?.invite){const i=c?.data?.invite;Object.entries(i).forEach(([d,f])=>{typeof f=="number"?l.setValue(d,String(f)):l.setValue(d,f)}),r.current=i}},[c]);const u=m.useCallback(Ce.debounce(async i=>{if(!Ce.isEqual(i,r.current)){a(!0);try{await o(i),r.current=i}finally{a(!1)}}},1e3),[o]),x=m.useCallback(i=>{u(i)},[u]);return m.useEffect(()=>{const i=l.watch(d=>{x(d)});return()=>i.unsubscribe()},[l.watch,x]),e.jsx(Ne,{...l,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{control:l.control,name:"invite_force",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("invite.invite_force.title")}),e.jsx(z,{children:s("invite.invite_force.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),e.jsx(b,{control:l.control,name:"invite_commission",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("invite.invite_commission.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("invite.invite_commission.placeholder"),...i,value:i.value||""})}),e.jsx(z,{children:s("invite.invite_commission.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"invite_gen_limit",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("invite.invite_gen_limit.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("invite.invite_gen_limit.placeholder"),...i,value:i.value||""})}),e.jsx(z,{children:s("invite.invite_gen_limit.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"invite_never_expire",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("invite.invite_never_expire.title")}),e.jsx(z,{children:s("invite.invite_never_expire.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),e.jsx(b,{control:l.control,name:"commission_first_time_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("invite.commission_first_time.title")}),e.jsx(z,{children:s("invite.commission_first_time.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),e.jsx(b,{control:l.control,name:"commission_auto_check_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("invite.commission_auto_check.title")}),e.jsx(z,{children:s("invite.commission_auto_check.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),e.jsx(b,{control:l.control,name:"commission_withdraw_limit",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("invite.commission_withdraw_limit.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("invite.commission_withdraw_limit.placeholder"),...i,value:i.value||""})}),e.jsx(z,{children:s("invite.commission_withdraw_limit.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"commission_withdraw_method",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("invite.commission_withdraw_method.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("invite.commission_withdraw_method.placeholder"),...i,value:Array.isArray(i.value)?i.value.join(","):"",onChange:d=>{const f=d.target.value.split(",").filter(Boolean);i.onChange(f),x(l.getValues())}})}),e.jsx(z,{children:s("invite.commission_withdraw_method.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"withdraw_close_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("invite.withdraw_close.title")}),e.jsx(z,{children:s("invite.withdraw_close.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),e.jsx(b,{control:l.control,name:"commission_distribution_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("invite.commission_distribution.title")}),e.jsx(z,{children:s("invite.commission_distribution.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:i.value,onCheckedChange:d=>{i.onChange(d),x(l.getValues())}})})]})}),l.watch("commission_distribution_enable")&&e.jsxs(e.Fragment,{children:[e.jsx(b,{control:l.control,name:"commission_distribution_l1",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:s("invite.commission_distribution.l1")}),e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:s("invite.commission_distribution.placeholder"),...i,value:i.value||"",onChange:d=>{const f=d.target.value?Number(d.target.value):0;i.onChange(f),x(l.getValues())}})}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"commission_distribution_l2",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:s("invite.commission_distribution.l2")}),e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:s("invite.commission_distribution.placeholder"),...i,value:i.value||"",onChange:d=>{const f=d.target.value?Number(d.target.value):0;i.onChange(f),x(l.getValues())}})}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"commission_distribution_l3",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:s("invite.commission_distribution.l3")}),e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:s("invite.commission_distribution.placeholder"),...i,value:i.value||"",onChange:d=>{const f=d.target.value?Number(d.target.value):0;i.onChange(f),x(l.getValues())}})}),e.jsx(k,{})]})})]}),n&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("invite.saving")})]})})}function Uu(){const{t:s}=M("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("invite.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("invite.description")})]}),e.jsx(Se,{}),e.jsx(Hu,{})]})}const Ku=Object.freeze(Object.defineProperty({__proto__:null,default:Uu},Symbol.toStringTag,{value:"Module"})),Bu=h.object({frontend_theme:h.string().nullable(),frontend_theme_sidebar:h.string().nullable(),frontend_theme_header:h.string().nullable(),frontend_theme_color:h.string().nullable(),frontend_background_url:h.string().url().nullable()}),Gu={frontend_theme:"",frontend_theme_sidebar:"",frontend_theme_header:"",frontend_theme_color:"",frontend_background_url:""};function Wu(){const{data:s}=ie({queryKey:["settings","frontend"],queryFn:()=>fs("frontend")}),n=fe({resolver:ye(Bu),defaultValues:Gu,mode:"onChange"});m.useEffect(()=>{if(s?.data?.frontend){const r=s?.data?.frontend;Object.entries(r).forEach(([l,c])=>{n.setValue(l,c)})}},[s]);function a(r){gs(r).then(({data:l})=>{l&&A.success("更新成功")})}return e.jsx(Ne,{...n,children:e.jsxs("form",{onSubmit:n.handleSubmit(a),className:"space-y-8",children:[e.jsx(b,{control:n.control,name:"frontend_theme_sidebar",render:({field:r})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:"边栏风格"}),e.jsx(z,{children:"边栏风格"})]}),e.jsx(N,{children:e.jsx(Q,{checked:r.value,onCheckedChange:r.onChange})})]})}),e.jsx(b,{control:n.control,name:"frontend_theme_header",render:({field:r})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:"头部风格"}),e.jsx(z,{children:"边栏风格"})]}),e.jsx(N,{children:e.jsx(Q,{checked:r.value,onCheckedChange:r.onChange})})]})}),e.jsx(b,{control:n.control,name:"frontend_theme_color",render:({field:r})=>e.jsxs(v,{children:[e.jsx(y,{children:"主题色"}),e.jsxs("div",{className:"relative w-max",children:[e.jsx(N,{children:e.jsxs("select",{className:_(tt({variant:"outline"}),"w-[200px] appearance-none font-normal"),...r,children:[e.jsx("option",{value:"default",children:"默认"}),e.jsx("option",{value:"black",children:"黑色"}),e.jsx("option",{value:"blackblue",children:"暗蓝色"}),e.jsx("option",{value:"green",children:"奶绿色"})]})}),e.jsx(Da,{className:"absolute right-3 top-2.5 h-4 w-4 opacity-50"})]}),e.jsx(z,{children:"主题色"}),e.jsx(k,{})]})}),e.jsx(b,{control:n.control,name:"frontend_background_url",render:({field:r})=>e.jsxs(v,{children:[e.jsx(y,{children:"背景"}),e.jsx(N,{children:e.jsx(T,{placeholder:"请输入图片地址",...r})}),e.jsx(z,{children:"将会在后台登录页面进行展示。"}),e.jsx(k,{})]})}),e.jsx(D,{type:"submit",children:"保存设置"})]})})}function Yu(){return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"个性化设置"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"自定义系统界面外观，包括主题风格、布局、颜色方案、背景图等个性化选项。"})]}),e.jsx(Se,{}),e.jsx(Wu,{})]})}const Qu=Object.freeze(Object.defineProperty({__proto__:null,default:Yu},Symbol.toStringTag,{value:"Module"})),Ju=h.object({server_pull_interval:h.coerce.number().nullable(),server_push_interval:h.coerce.number().nullable(),server_token:h.string().nullable(),device_limit_mode:h.coerce.number().nullable()}),Zu={server_pull_interval:0,server_push_interval:0,server_token:"",device_limit_mode:0};function Xu(){const{t:s}=M("settings"),[n,a]=m.useState(!1),r=m.useRef(null),l=fe({resolver:ye(Ju),defaultValues:Zu,mode:"onBlur"}),{data:c}=ie({queryKey:["settings","server"],queryFn:()=>fs("server")}),{mutateAsync:o}=Ze({mutationFn:gs,onSuccess:d=>{d.data&&A.success(s("common.AutoSaved"))}});m.useEffect(()=>{if(c?.data.server){const d=c.data.server;Object.entries(d).forEach(([f,w])=>{l.setValue(f,w)}),r.current=d}},[c]);const u=m.useCallback(Ce.debounce(async d=>{if(!Ce.isEqual(d,r.current)){a(!0);try{await o(d),r.current=d}finally{a(!1)}}},1e3),[o]),x=m.useCallback(d=>{u(d)},[u]);m.useEffect(()=>{const d=l.watch(f=>{x(f)});return()=>d.unsubscribe()},[l.watch,x]);const i=()=>{const d=Math.floor(Math.random()*17)+16,f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";let w="";for(let P=0;P<d;P++)w+=f.charAt(Math.floor(Math.random()*f.length));l.setValue("server_token",w)};return e.jsx(Ne,{...l,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{control:l.control,name:"server_token",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("server.server_token.title")}),e.jsx(N,{children:e.jsxs("div",{className:"relative",children:[e.jsx(T,{placeholder:s("server.server_token.placeholder"),...d,value:d.value||"",className:"pr-10"}),e.jsx(je,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(X,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2",onClick:f=>{f.preventDefault(),i()},children:e.jsx(bo,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"})})}),e.jsx(ce,{children:e.jsx("p",{children:s("server.server_token.generate_tooltip")})})]})})]})}),e.jsx(z,{children:s("server.server_token.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"server_pull_interval",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("server.server_pull_interval.title")}),e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:s("server.server_pull_interval.placeholder"),...d,value:d.value||"",onChange:f=>{const w=f.target.value?Number(f.target.value):null;d.onChange(w)}})}),e.jsx(z,{children:s("server.server_pull_interval.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"server_push_interval",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("server.server_push_interval.title")}),e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:s("server.server_push_interval.placeholder"),...d,value:d.value||"",onChange:f=>{const w=f.target.value?Number(f.target.value):null;d.onChange(w)}})}),e.jsx(z,{children:s("server.server_push_interval.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"device_limit_mode",render:({field:d})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("server.device_limit_mode.title")}),e.jsxs(J,{onValueChange:d.onChange,value:d.value?.toString()||"0",children:[e.jsx(N,{children:e.jsx(W,{children:e.jsx(Z,{placeholder:s("server.device_limit_mode.placeholder")})})}),e.jsxs(Y,{children:[e.jsx(q,{value:"0",children:s("server.device_limit_mode.strict")}),e.jsx(q,{value:"1",children:s("server.device_limit_mode.relaxed")})]})]}),e.jsx(z,{children:s("server.device_limit_mode.description")}),e.jsx(k,{})]})}),n&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("server.saving")})]})})}function em(){const{t:s}=M("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("server.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("server.description")})]}),e.jsx(Se,{}),e.jsx(Xu,{})]})}const sm=Object.freeze(Object.defineProperty({__proto__:null,default:em},Symbol.toStringTag,{value:"Module"}));function tm({open:s,onOpenChange:n,result:a}){const r=!a.error;return e.jsx(ve,{open:s,onOpenChange:n,children:e.jsxs(pe,{className:"sm:max-w-[425px]",children:[e.jsxs(we,{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[r?e.jsx(tr,{className:"h-5 w-5 text-green-500"}):e.jsx(ar,{className:"h-5 w-5 text-destructive"}),e.jsx(be,{children:r?"邮件发送成功":"邮件发送失败"})]}),e.jsx(ke,{children:r?"测试邮件已成功发送，请检查收件箱":"发送测试邮件时遇到错误"})]}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx("div",{className:"font-medium",children:"发送详情"}),e.jsxs("div",{className:"grid grid-cols-[100px_1fr] items-center gap-2 text-sm",children:[e.jsx("div",{className:"text-muted-foreground",children:"收件地址"}),e.jsx("div",{children:a.email}),e.jsx("div",{className:"text-muted-foreground",children:"邮件主题"}),e.jsx("div",{children:a.subject}),e.jsx("div",{className:"text-muted-foreground",children:"模板名称"}),e.jsx("div",{children:a.template_name})]})]}),a.error&&e.jsxs("div",{className:"grid gap-2",children:[e.jsx("div",{className:"font-medium text-destructive",children:"错误信息"}),e.jsx("div",{className:"rounded-md bg-destructive/10 p-3 text-sm text-destructive break-all",children:a.error})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx("div",{className:"font-medium",children:"配置信息"}),e.jsx(zs,{className:"h-[200px] rounded-md border p-4",children:e.jsx("div",{className:"grid gap-2 text-sm",children:e.jsxs("div",{className:"grid grid-cols-[100px_1fr] items-center gap-2",children:[e.jsx("div",{className:"text-muted-foreground",children:"驱动"}),e.jsx("div",{children:a.config.driver}),e.jsx("div",{className:"text-muted-foreground",children:"服务器"}),e.jsx("div",{children:a.config.host}),e.jsx("div",{className:"text-muted-foreground",children:"端口"}),e.jsx("div",{children:a.config.port}),e.jsx("div",{className:"text-muted-foreground",children:"加密方式"}),e.jsx("div",{children:a.config.encryption||"无"}),e.jsx("div",{className:"text-muted-foreground",children:"发件人"}),e.jsx("div",{children:a.config.from.address?`${a.config.from.address}${a.config.from.name?` (${a.config.from.name})`:""}`:"未设置"}),e.jsx("div",{className:"text-muted-foreground",children:"用户名"}),e.jsx("div",{children:a.config.username||"未设置"})]})})})]})]})]})})}const am=h.object({email_template:h.string().nullable().default("classic"),email_host:h.string().nullable().default(""),email_port:h.coerce.number().nullable().default(465),email_username:h.string().nullable().default(""),email_password:h.string().nullable().default(""),email_encryption:h.string().nullable().default(""),email_from_address:h.string().email().nullable().default(""),remind_mail_enable:h.boolean().nullable().default(!1)});function nm(){const{t:s}=M("settings"),[n,a]=m.useState(null),[r,l]=m.useState(!1),c=m.useRef(null),[o,u]=m.useState(!1),x=fe({resolver:ye(am),defaultValues:{},mode:"onBlur"}),{data:i}=ie({queryKey:["settings","email"],queryFn:()=>fs("email")}),{data:d}=ie({queryKey:["emailTemplate"],queryFn:()=>jd()}),{mutateAsync:f}=Ze({mutationFn:gs,onSuccess:g=>{g.data&&A.success(s("common.autoSaved"))}}),{mutate:w,isPending:P}=Ze({mutationFn:vd,onMutate:()=>{a(null),l(!1)},onSuccess:g=>{a(g.data),l(!0),g.data.error?A.error(s("email.test.error")):A.success(s("email.test.success"))}});m.useEffect(()=>{if(i?.data.email){const g=i.data.email;Object.entries(g).forEach(([j,S])=>{x.setValue(j,S)}),c.current=g}},[i]);const C=m.useCallback(Ce.debounce(async g=>{if(!Ce.isEqual(g,c.current)){u(!0);try{await f(g),c.current=g}finally{u(!1)}}},1e3),[f]),p=m.useCallback(g=>{C(g)},[C]);return m.useEffect(()=>{const g=x.watch(j=>{p(j)});return()=>g.unsubscribe()},[x.watch,p]),e.jsxs(e.Fragment,{children:[e.jsx(Ne,{...x,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{control:x.control,name:"email_host",render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("email.email_host.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("common.placeholder"),...g,value:g.value||""})}),e.jsx(z,{children:s("email.email_host.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"email_port",render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("email.email_port.title")}),e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:s("common.placeholder"),...g,value:g.value||"",onChange:j=>{const S=j.target.value?Number(j.target.value):null;g.onChange(S)}})}),e.jsx(z,{children:s("email.email_port.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"email_encryption",render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("email.email_encryption.title")}),e.jsxs(J,{onValueChange:g.onChange,value:g.value||"none",children:[e.jsx(N,{children:e.jsx(W,{children:e.jsx(Z,{placeholder:"请选择加密方式"})})}),e.jsxs(Y,{children:[e.jsx(q,{value:"none",children:s("email.email_encryption.none")}),e.jsx(q,{value:"ssl",children:s("email.email_encryption.ssl")}),e.jsx(q,{value:"tls",children:s("email.email_encryption.tls")})]})]}),e.jsx(z,{children:s("email.email_encryption.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"email_username",render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("email.email_username.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("common.placeholder"),...g,value:g.value||""})}),e.jsx(z,{children:s("email.email_username.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"email_password",render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("email.email_password.title")}),e.jsx(N,{children:e.jsx(T,{type:"password",placeholder:s("common.placeholder"),...g,value:g.value||""})}),e.jsx(z,{children:s("email.email_password.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"email_from_address",render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("email.email_from.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("common.placeholder"),...g,value:g.value||""})}),e.jsx(z,{children:s("email.email_from.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"email_template",render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("email.email_template.title")}),e.jsxs(J,{onValueChange:j=>{g.onChange(j),p(x.getValues())},value:g.value||void 0,children:[e.jsx(N,{children:e.jsx(W,{className:"w-[200px]",children:e.jsx(Z,{placeholder:s("email.email_template.placeholder")})})}),e.jsx(Y,{children:d?.data?.map(j=>e.jsx(q,{value:j,children:j},j))})]}),e.jsx(z,{children:s("email.email_template.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"remind_mail_enable",render:({field:g})=>e.jsxs(v,{children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:s("email.remind_mail.title")}),e.jsx(z,{children:s("email.remind_mail.description")})]}),e.jsx(N,{children:e.jsx(Q,{checked:g.value||!1,onCheckedChange:j=>{g.onChange(j),p(x.getValues())}})})]})}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(D,{onClick:()=>w(),loading:P,disabled:P,children:s(P?"test.sending":"test.title")})})]})}),o&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("saving")}),n&&e.jsx(tm,{open:r,onOpenChange:l,result:n})]})}function rm(){const{t:s}=M("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("email.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("email.description")})]}),e.jsx(Se,{}),e.jsx(nm,{})]})}const lm=Object.freeze(Object.defineProperty({__proto__:null,default:rm},Symbol.toStringTag,{value:"Module"})),im=h.object({telegram_bot_enable:h.boolean().nullable(),telegram_bot_token:h.string().nullable(),telegram_discuss_link:h.string().nullable()}),om={telegram_bot_enable:!1,telegram_bot_token:"",telegram_discuss_link:""};function cm(){const{t:s}=M("settings"),[n,a]=m.useState(!1),r=m.useRef(null),l=fe({resolver:ye(im),defaultValues:om,mode:"onBlur"}),{data:c}=ie({queryKey:["settings","telegram"],queryFn:()=>fs("telegram")}),{mutateAsync:o}=Ze({mutationFn:gs,onSuccess:f=>{f.data&&A.success(s("common.autoSaved"))}}),{mutate:u,isPending:x}=Ze({mutationFn:bd,onSuccess:f=>{f.data&&A.success(s("telegram.webhook.success"))}});m.useEffect(()=>{if(c?.data.telegram){const f=c.data.telegram;Object.entries(f).forEach(([w,P])=>{l.setValue(w,P)}),r.current=f}},[c]);const i=m.useCallback(Ce.debounce(async f=>{if(!Ce.isEqual(f,r.current)){a(!0);try{await o(f),r.current=f}finally{a(!1)}}},1e3),[o]),d=m.useCallback(f=>{i(f)},[i]);return m.useEffect(()=>{const f=l.watch(w=>{d(w)});return()=>f.unsubscribe()},[l.watch,d]),e.jsx(Ne,{...l,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{control:l.control,name:"telegram_bot_token",render:({field:f})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("telegram.bot_token.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("telegram.bot_token.placeholder"),...f,value:f.value||""})}),e.jsx(z,{children:s("telegram.bot_token.description")}),e.jsx(k,{})]})}),l.watch("telegram_bot_token")&&e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("telegram.webhook.title")}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(D,{loading:x,disabled:x,onClick:()=>u(),children:s(x?"telegram.webhook.setting":"telegram.webhook.button")}),n&&e.jsx("span",{className:"text-sm text-muted-foreground",children:s("common.saving")})]}),e.jsx(z,{children:s("telegram.webhook.description")}),e.jsx(k,{})]}),e.jsx(b,{control:l.control,name:"telegram_bot_enable",render:({field:f})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("telegram.bot_enable.title")}),e.jsx(z,{children:s("telegram.bot_enable.description")}),e.jsx(N,{children:e.jsx(Q,{checked:f.value||!1,onCheckedChange:w=>{f.onChange(w),d(l.getValues())}})}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"telegram_discuss_link",render:({field:f})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("telegram.discuss_link.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("telegram.discuss_link.placeholder"),...f,value:f.value||""})}),e.jsx(z,{children:s("telegram.discuss_link.description")}),e.jsx(k,{})]})}),n&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("common.saving")})]})})}function dm(){const{t:s}=M("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("telegram.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("telegram.description")})]}),e.jsx(Se,{}),e.jsx(cm,{})]})}const um=Object.freeze(Object.defineProperty({__proto__:null,default:dm},Symbol.toStringTag,{value:"Module"})),mm=h.object({windows_version:h.string().nullable(),windows_download_url:h.string().nullable(),macos_version:h.string().nullable(),macos_download_url:h.string().nullable(),android_version:h.string().nullable(),android_download_url:h.string().nullable()}),xm={windows_version:"",windows_download_url:"",macos_version:"",macos_download_url:"",android_version:"",android_download_url:""};function hm(){const{t:s}=M("settings"),[n,a]=m.useState(!1),r=m.useRef(null),l=fe({resolver:ye(mm),defaultValues:xm,mode:"onBlur"}),{data:c}=ie({queryKey:["settings","app"],queryFn:()=>fs("app")}),{mutateAsync:o}=Ze({mutationFn:gs,onSuccess:i=>{i.data&&A.success(s("app.save_success"))}});m.useEffect(()=>{if(c?.data.app){const i=c.data.app;Object.entries(i).forEach(([d,f])=>{l.setValue(d,f)}),r.current=i}},[c]);const u=m.useCallback(Ce.debounce(async i=>{if(!Ce.isEqual(i,r.current)){a(!0);try{await o(i),r.current=i}finally{a(!1)}}},1e3),[o]),x=m.useCallback(i=>{u(i)},[u]);return m.useEffect(()=>{const i=l.watch(d=>{x(d)});return()=>i.unsubscribe()},[l.watch,x]),e.jsx(Ne,{...l,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{control:l.control,name:"windows_version",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("app.windows.version.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(z,{children:s("app.windows.version.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"windows_download_url",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("app.windows.download.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(z,{children:s("app.windows.download.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"macos_version",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("app.macos.version.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(z,{children:s("app.macos.version.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"macos_download_url",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("app.macos.download.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(z,{children:s("app.macos.download.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"android_version",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("app.android.version.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(z,{children:s("app.android.version.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"android_download_url",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{className:"text-base",children:s("app.android.download.title")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("common.placeholder"),...i,value:i.value||""})}),e.jsx(z,{children:s("app.android.download.description")}),e.jsx(k,{})]})}),n&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("common.saving")})]})})}function pm(){const{t:s}=M("settings");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:s("app.title")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s("app.description")})]}),e.jsx(Se,{}),e.jsx(hm,{})]})}const fm=Object.freeze(Object.defineProperty({__proto__:null,default:pm},Symbol.toStringTag,{value:"Module"})),Va=m.forwardRef(({className:s,...n},a)=>e.jsx("div",{className:"relative w-full overflow-auto",children:e.jsx("table",{ref:a,className:_("w-full caption-bottom text-sm",s),...n})}));Va.displayName="Table";const Ma=m.forwardRef(({className:s,...n},a)=>e.jsx("thead",{ref:a,className:_("[&_tr]:border-b",s),...n}));Ma.displayName="TableHeader";const Fa=m.forwardRef(({className:s,...n},a)=>e.jsx("tbody",{ref:a,className:_("[&_tr:last-child]:border-0",s),...n}));Fa.displayName="TableBody";const gm=m.forwardRef(({className:s,...n},a)=>e.jsx("tfoot",{ref:a,className:_("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...n}));gm.displayName="TableFooter";const Rs=m.forwardRef(({className:s,...n},a)=>e.jsx("tr",{ref:a,className:_("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...n}));Rs.displayName="TableRow";const Oa=m.forwardRef(({className:s,...n},a)=>e.jsx("th",{ref:a,className:_("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...n}));Oa.displayName="TableHead";const Js=m.forwardRef(({className:s,...n},a)=>e.jsx("td",{ref:a,className:_("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...n}));Js.displayName="TableCell";const jm=m.forwardRef(({className:s,...n},a)=>e.jsx("caption",{ref:a,className:_("mt-4 text-sm text-muted-foreground",s),...n}));jm.displayName="TableCaption";function vm({table:s}){const[n,a]=m.useState(""),{t:r}=M("common");m.useEffect(()=>{a((s.getState().pagination.pageIndex+1).toString())},[s.getState().pagination.pageIndex]);const l=c=>{const o=parseInt(c);!isNaN(o)&&o>=1&&o<=s.getPageCount()?s.setPageIndex(o-1):a((s.getState().pagination.pageIndex+1).toString())};return e.jsxs("div",{className:"flex flex-col-reverse gap-4 px-2 py-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("div",{className:"flex-1 text-sm text-muted-foreground",children:r("table.pagination.selected",{selected:s.getFilteredSelectedRowModel().rows.length,total:s.getFilteredRowModel().rows.length})}),e.jsxs("div",{className:"flex flex-col-reverse items-center gap-4 sm:flex-row sm:gap-6 lg:gap-8",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("p",{className:"text-sm font-medium",children:r("table.pagination.itemsPerPage")}),e.jsxs(J,{value:`${s.getState().pagination.pageSize}`,onValueChange:c=>{s.setPageSize(Number(c))},children:[e.jsx(W,{className:"h-8 w-[70px]",children:e.jsx(Z,{placeholder:s.getState().pagination.pageSize})}),e.jsx(Y,{side:"top",children:[10,20,30,40,50,100,500].map(c=>e.jsx(q,{value:`${c}`,children:c},c))})]})]}),e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-sm font-medium",children:[e.jsx("span",{children:r("table.pagination.page")}),e.jsx(T,{type:"text",value:n,onChange:c=>a(c.target.value),onBlur:c=>l(c.target.value),onKeyDown:c=>{c.key==="Enter"&&l(c.currentTarget.value)},className:"h-8 w-[50px] text-center"}),e.jsx("span",{children:r("table.pagination.pageOf",{total:s.getPageCount()})})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(D,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>s.setPageIndex(0),disabled:!s.getCanPreviousPage(),children:[e.jsx("span",{className:"sr-only",children:r("table.pagination.firstPage")}),e.jsx(yo,{className:"h-4 w-4"})]}),e.jsxs(D,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>s.previousPage(),disabled:!s.getCanPreviousPage(),children:[e.jsx("span",{className:"sr-only",children:r("table.pagination.previousPage")}),e.jsx(Xn,{className:"h-4 w-4"})]}),e.jsxs(D,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>s.nextPage(),disabled:!s.getCanNextPage(),children:[e.jsx("span",{className:"sr-only",children:r("table.pagination.nextPage")}),e.jsx(ka,{className:"h-4 w-4"})]}),e.jsxs(D,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>s.setPageIndex(s.getPageCount()-1),disabled:!s.getCanNextPage(),children:[e.jsx("span",{className:"sr-only",children:r("table.pagination.lastPage")}),e.jsx(No,{className:"h-4 w-4"})]})]})]})]})}function xs({table:s,toolbar:n,draggable:a=!1,onDragStart:r,onDragEnd:l,onDragOver:c,onDragLeave:o,onDrop:u,showPagination:x=!0,isLoading:i=!1}){const{t:d}=M("common"),f=m.useRef(null),w=s.getAllColumns().filter(g=>g.getIsPinned()==="left"),P=s.getAllColumns().filter(g=>g.getIsPinned()==="right"),C=g=>w.slice(0,g).reduce((j,S)=>j+(S.getSize()??0),0),p=g=>P.slice(g+1).reduce((j,S)=>j+(S.getSize()??0),0);return e.jsxs("div",{className:"space-y-4",children:[typeof n=="function"?n(s):n,e.jsx("div",{ref:f,className:"relative overflow-auto rounded-md border bg-card",children:e.jsx("div",{className:"overflow-auto",children:e.jsxs(Va,{children:[e.jsx(Ma,{children:s.getHeaderGroups().map(g=>e.jsx(Rs,{className:"hover:bg-transparent",children:g.headers.map((j,S)=>{const R=j.column.getIsPinned()==="left",E=j.column.getIsPinned()==="right",F=R?C(w.indexOf(j.column)):void 0,H=E?p(P.indexOf(j.column)):void 0;return e.jsx(Oa,{colSpan:j.colSpan,style:{width:j.getSize(),...R&&{left:F},...E&&{right:H}},className:_("h-11 bg-card px-4 text-muted-foreground",(R||E)&&["sticky z-20","before:absolute before:bottom-0 before:top-0 before:w-[1px] before:bg-border",R&&"before:right-0",E&&"before:left-0"]),children:j.isPlaceholder?null:Vt(j.column.columnDef.header,j.getContext())},j.id)})},g.id))}),e.jsx(Fa,{children:s.getRowModel().rows?.length?s.getRowModel().rows.map((g,j)=>e.jsx(Rs,{"data-state":g.getIsSelected()&&"selected",className:"hover:bg-muted/50",draggable:a,onDragStart:S=>r?.(S,j),onDragEnd:l,onDragOver:c,onDragLeave:o,onDrop:S=>u?.(S,j),children:g.getVisibleCells().map((S,R)=>{const E=S.column.getIsPinned()==="left",F=S.column.getIsPinned()==="right",H=E?C(w.indexOf(S.column)):void 0,te=F?p(P.indexOf(S.column)):void 0;return e.jsx(Js,{style:{width:S.column.getSize(),...E&&{left:H},...F&&{right:te}},className:_("bg-card",(E||F)&&["sticky z-20","before:absolute before:bottom-0 before:top-0 before:w-[1px] before:bg-border",E&&"before:right-0",F&&"before:left-0"]),children:Vt(S.column.columnDef.cell,S.getContext())},S.id)})},g.id)):e.jsx(Rs,{children:e.jsx(Js,{colSpan:s.getAllColumns().length,className:"h-24 text-center",children:d("table.noData")})})})]})})}),x&&e.jsx(vm,{table:s})]})}const bm=s=>h.object({id:h.number().nullable(),name:h.string().min(2,s("form.validation.name.min")).max(30,s("form.validation.name.max")),icon:h.string().optional().nullable(),notify_domain:h.string().refine(a=>!a||/^https?:\/\/\S+/.test(a),s("form.validation.notify_domain.url")).optional().nullable(),handling_fee_fixed:h.coerce.number().min(0).optional().nullable(),handling_fee_percent:h.coerce.number().min(0).max(100).optional().nullable(),payment:h.string().min(1,s("form.validation.payment.required")),config:h.record(h.string(),h.string())}),on={id:null,name:"",icon:"",notify_domain:"",handling_fee_fixed:0,handling_fee_percent:0,payment:"",config:{}};function Ur({refetch:s,dialogTrigger:n,type:a="add",defaultFormValues:r=on}){const{t:l}=M("payment"),[c,o]=m.useState(!1),[u,x]=m.useState(!1),[i,d]=m.useState([]),[f,w]=m.useState([]),P=bm(l),C=fe({resolver:ye(P),defaultValues:r,mode:"onChange"}),p=C.watch("payment");m.useEffect(()=>{c&&(async()=>{const{data:S}=await Oc();d(S)})()},[c]),m.useEffect(()=>{if(!p||!c)return;(async()=>{const S={payment:p,...a==="edit"&&{id:Number(C.getValues("id"))}};zc(S).then(({data:R})=>{w(R);const E=R.reduce((F,H)=>(H.field_name&&(F[H.field_name]=H.value??""),F),{});C.setValue("config",E)})})()},[p,c,C,a]);const g=async j=>{x(!0);try{(await Lc(j)).data&&(A.success(l("form.messages.success")),C.reset(on),s(),o(!1))}finally{x(!1)}};return e.jsxs(ve,{open:c,onOpenChange:o,children:[e.jsx(Ye,{asChild:!0,children:n||e.jsxs(D,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(Re,{icon:"ion:add"})," ",e.jsx("div",{children:l("form.add.button")})]})}),e.jsxs(pe,{className:"sm:max-w-[425px]",children:[e.jsx(we,{children:e.jsx(be,{children:l(a==="add"?"form.add.title":"form.edit.title")})}),e.jsx(Ne,{...C,children:e.jsxs("form",{onSubmit:C.handleSubmit(g),className:"space-y-4",children:[e.jsx(b,{control:C.control,name:"name",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.name.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:l("form.fields.name.placeholder"),...j})}),e.jsx(z,{children:l("form.fields.name.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:C.control,name:"icon",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.icon.label")}),e.jsx(N,{children:e.jsx(T,{...j,value:j.value||"",placeholder:l("form.fields.icon.placeholder")})}),e.jsx(z,{children:l("form.fields.icon.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:C.control,name:"notify_domain",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.notify_domain.label")}),e.jsx(N,{children:e.jsx(T,{...j,value:j.value||"",placeholder:l("form.fields.notify_domain.placeholder")})}),e.jsx(z,{children:l("form.fields.notify_domain.description")}),e.jsx(k,{})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(b,{control:C.control,name:"handling_fee_percent",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.handling_fee_percent.label")}),e.jsx(N,{children:e.jsx(T,{type:"number",...j,value:j.value||"",placeholder:l("form.fields.handling_fee_percent.placeholder")})}),e.jsx(k,{})]})}),e.jsx(b,{control:C.control,name:"handling_fee_fixed",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.handling_fee_fixed.label")}),e.jsx(N,{children:e.jsx(T,{type:"number",...j,value:j.value||"",placeholder:l("form.fields.handling_fee_fixed.placeholder")})}),e.jsx(k,{})]})})]}),e.jsx(b,{control:C.control,name:"payment",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.payment.label")}),e.jsxs(J,{onValueChange:j.onChange,defaultValue:j.value,children:[e.jsx(N,{children:e.jsx(W,{children:e.jsx(Z,{placeholder:l("form.fields.payment.placeholder")})})}),e.jsx(Y,{children:i.map(S=>e.jsx(q,{value:S,children:S},S))})]}),e.jsx(z,{children:l("form.fields.payment.description")}),e.jsx(k,{})]})}),f.length>0&&e.jsx("div",{className:"space-y-4",children:f.map(j=>e.jsx(b,{control:C.control,name:`config.${j.field_name}`,render:({field:S})=>e.jsxs(v,{children:[e.jsx(y,{children:j.label}),e.jsx(N,{children:e.jsx(T,{...S,value:S.value||""})}),e.jsx(k,{})]})},j.field_name))}),e.jsxs(Oe,{children:[e.jsx(Nt,{asChild:!0,children:e.jsx(D,{type:"button",variant:"outline",children:l("form.buttons.cancel")})}),e.jsx(D,{type:"submit",disabled:u,children:l("form.buttons.submit")})]})]})})]})]})}function L({column:s,title:n,tooltip:a,className:r}){return s.getCanSort()?e.jsx("div",{className:"flex items-center gap-1",children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(D,{variant:"ghost",size:"default",className:_("-ml-3 flex h-8 items-center gap-2 text-nowrap font-medium hover:bg-muted/60",r),onClick:()=>s.toggleSorting(s.getIsSorted()==="asc"),children:[e.jsx("span",{children:n}),a&&e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(Qa,{className:"h-4 w-4 cursor-pointer text-muted-foreground"})}),e.jsx(ce,{children:a})]})}),s.getIsSorted()==="asc"?e.jsx(fa,{className:"h-4 w-4 text-foreground/70"}):s.getIsSorted()==="desc"?e.jsx(ga,{className:"h-4 w-4 text-foreground/70"}):e.jsx(_o,{className:"h-4 w-4 text-muted-foreground/70 transition-colors hover:text-foreground/70"})]})})}):e.jsxs("div",{className:_("flex items-center space-x-1 text-nowrap py-2 font-medium text-muted-foreground",r),children:[e.jsx("span",{children:n}),a&&e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{children:e.jsx(Qa,{className:"h-4 w-4 text-muted-foreground"})}),e.jsx(ce,{children:a})]})})]})}const za=wo,Kr=Co,ym=So,Br=m.forwardRef(({className:s,...n},a)=>e.jsx(rr,{className:_("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...n,ref:a}));Br.displayName=rr.displayName;const Qt=m.forwardRef(({className:s,...n},a)=>e.jsxs(ym,{children:[e.jsx(Br,{}),e.jsx(lr,{ref:a,className:_("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...n})]}));Qt.displayName=lr.displayName;const Jt=({className:s,...n})=>e.jsx("div",{className:_("flex flex-col space-y-2 text-center sm:text-left",s),...n});Jt.displayName="AlertDialogHeader";const Zt=({className:s,...n})=>e.jsx("div",{className:_("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...n});Zt.displayName="AlertDialogFooter";const Xt=m.forwardRef(({className:s,...n},a)=>e.jsx(ir,{ref:a,className:_("text-lg font-semibold",s),...n}));Xt.displayName=ir.displayName;const ea=m.forwardRef(({className:s,...n},a)=>e.jsx(or,{ref:a,className:_("text-sm text-muted-foreground",s),...n}));ea.displayName=or.displayName;const sa=m.forwardRef(({className:s,...n},a)=>e.jsx(cr,{ref:a,className:_(Xs(),s),...n}));sa.displayName=cr.displayName;const ta=m.forwardRef(({className:s,...n},a)=>e.jsx(dr,{ref:a,className:_(Xs({variant:"outline"}),"mt-2 sm:mt-0",s),...n}));ta.displayName=dr.displayName;function We({onConfirm:s,children:n,title:a="确认操作",description:r="确定要执行此操作吗？",cancelText:l="取消",confirmText:c="确认",variant:o="default",className:u}){return e.jsxs(za,{children:[e.jsx(Kr,{asChild:!0,children:n}),e.jsxs(Qt,{className:_("sm:max-w-[425px]",u),children:[e.jsxs(Jt,{children:[e.jsx(Xt,{children:a}),e.jsx(ea,{children:r})]}),e.jsxs(Zt,{children:[e.jsx(ta,{asChild:!0,children:e.jsx(D,{variant:"outline",children:l})}),e.jsx(sa,{asChild:!0,children:e.jsx(D,{variant:o,onClick:s,children:c})})]})]})]})}const Gr=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M11.29 15.29a2 2 0 0 0-.12.15a.8.8 0 0 0-.09.18a.6.6 0 0 0-.06.18a1.4 1.4 0 0 0 0 .2a.84.84 0 0 0 .08.38a.9.9 0 0 0 .54.54a.94.94 0 0 0 .76 0a.9.9 0 0 0 .54-.54A1 1 0 0 0 13 16a1 1 0 0 0-.29-.71a1 1 0 0 0-1.42 0M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2m0 18a8 8 0 1 1 8-8a8 8 0 0 1-8 8m0-13a3 3 0 0 0-2.6 1.5a1 1 0 1 0 1.73 1A1 1 0 0 1 12 9a1 1 0 0 1 0 2a1 1 0 0 0-1 1v1a1 1 0 0 0 2 0v-.18A3 3 0 0 0 12 7"})}),Nm=({refetch:s,isSortMode:n=!1})=>{const{t:a}=M("payment");return[{id:"drag-handle",header:()=>null,cell:()=>e.jsx("div",{className:n?"cursor-move":"opacity-0",children:e.jsx(Kt,{className:"size-4"})}),size:40,enableSorting:!1},{accessorKey:"id",header:({column:r})=>e.jsx(L,{column:r,title:a("table.columns.id")}),cell:({row:r})=>e.jsx(K,{variant:"outline",children:r.getValue("id")}),enableSorting:!0,size:60},{accessorKey:"enable",header:({column:r})=>e.jsx(L,{column:r,title:a("table.columns.enable")}),cell:({row:r})=>e.jsx(Q,{defaultChecked:r.getValue("enable"),onCheckedChange:async()=>{const{data:l}=await $c({id:r.original.id});l||s()}}),enableSorting:!1,size:100},{accessorKey:"name",header:({column:r})=>e.jsx(L,{column:r,title:a("table.columns.name")}),cell:({row:r})=>e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"max-w-[200px] truncate font-medium",children:r.getValue("name")})}),enableSorting:!1,size:200},{accessorKey:"payment",header:({column:r})=>e.jsx(L,{column:r,title:a("table.columns.payment")}),cell:({row:r})=>e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"max-w-[200px] truncate font-medium",children:r.getValue("payment")})}),enableSorting:!1,size:200},{accessorKey:"notify_url",header:({column:r})=>e.jsxs("div",{className:"flex items-center",children:[e.jsx(L,{column:r,title:a("table.columns.notify_url")}),e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{className:"ml-1",children:e.jsx(Gr,{className:"h-4 w-4"})}),e.jsx(ce,{children:a("table.columns.notify_url_tooltip")})]})})]}),cell:({row:r})=>e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"max-w-[300px] truncate font-medium",children:r.getValue("notify_url")})}),enableSorting:!1,size:3e3},{id:"actions",header:({column:r})=>e.jsx(L,{className:"justify-end",column:r,title:a("table.columns.actions")}),cell:({row:r})=>e.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[e.jsx(Ur,{refetch:s,dialogTrigger:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:[e.jsx(qs,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:a("table.actions.edit")})]}),type:"edit",defaultFormValues:r.original}),e.jsx(We,{title:a("table.actions.delete.title"),description:a("table.actions.delete.description"),onConfirm:async()=>{const{data:l}=await Ac({id:r.original.id});l&&s()},children:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-destructive/10",children:[e.jsx(os,{className:"h-4 w-4 text-muted-foreground hover:text-destructive"}),e.jsx("span",{className:"sr-only",children:a("table.actions.delete.title")})]})})]}),size:100}]};function _m({table:s,refetch:n,saveOrder:a,isSortMode:r}){const{t:l}=M("payment"),c=s.getState().columnFilters.length>0;return e.jsxs("div",{className:"flex items-center justify-between",children:[r?e.jsx("p",{className:"text-sm text-muted-foreground",children:l("table.toolbar.sort.hint")}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ur,{refetch:n}),e.jsx(T,{placeholder:l("table.toolbar.search"),value:s.getColumn("name")?.getFilterValue()??"",onChange:o=>s.getColumn("name")?.setFilterValue(o.target.value),className:"h-8 w-[250px]"}),c&&e.jsxs(D,{variant:"ghost",onClick:()=>s.resetColumnFilters(),children:[l("table.toolbar.reset"),e.jsx(Xe,{className:"ml-2 h-4 w-4"})]})]}),s.getRowCount()>0&&e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(D,{variant:r?"default":"outline",onClick:a,size:"sm",children:l(r?"table.toolbar.sort.save":"table.toolbar.sort.edit")})})]})}function wm(){const[s,n]=m.useState([]),[a,r]=m.useState([]),[l,c]=m.useState(!1),[o,u]=m.useState([]),[x,i]=m.useState({"drag-handle":!1}),[d,f]=m.useState({pageSize:20,pageIndex:0}),{refetch:w}=ie({queryKey:["paymentList"],queryFn:async()=>{const{data:j}=await Fc();return u(j?.map(S=>({...S,enable:!!S.enable}))||[]),j}});m.useEffect(()=>{i({"drag-handle":l,actions:!l}),f({pageSize:l?99999:10,pageIndex:0})},[l]);const P=(j,S)=>{l&&(j.dataTransfer.setData("text/plain",S.toString()),j.currentTarget.classList.add("opacity-50"))},C=(j,S)=>{if(!l)return;j.preventDefault(),j.currentTarget.classList.remove("bg-muted");const R=parseInt(j.dataTransfer.getData("text/plain"));if(R===S)return;const E=[...o],[F]=E.splice(R,1);E.splice(S,0,F),u(E)},p=async()=>{l?qc({ids:o.map(j=>j.id)}).then(()=>{w(),c(!1),A.success("排序保存成功")}):c(!0)},g=ts({data:o,columns:Nm({refetch:w,isSortMode:l}),state:{sorting:a,columnFilters:s,columnVisibility:x,pagination:d},onSortingChange:r,onColumnFiltersChange:n,onColumnVisibilityChange:i,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),getSortedRowModel:us(),initialState:{columnPinning:{right:["actions"]}},pageCount:l?1:void 0});return e.jsx(xs,{table:g,toolbar:j=>e.jsx(_m,{table:j,refetch:w,saveOrder:p,isSortMode:l}),draggable:l,onDragStart:P,onDragEnd:j=>j.currentTarget.classList.remove("opacity-50"),onDragOver:j=>{j.preventDefault(),j.currentTarget.classList.add("bg-muted")},onDragLeave:j=>j.currentTarget.classList.remove("bg-muted"),onDrop:C,showPagination:!l})}function Cm(){const{t:s}=M("payment");return e.jsxs(Pe,{children:[e.jsxs(Ee,{className:"flex items-center justify-between",children:[e.jsx(qe,{}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{children:[e.jsx("header",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("div",{className:"mb-2",children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")})}),e.jsx("p",{className:"text-muted-foreground",children:s("description")})]})}),e.jsx("section",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(wm,{})})]})]})}const Sm=Object.freeze(Object.defineProperty({__proto__:null,default:Cm},Symbol.toStringTag,{value:"Module"}));function km({pluginName:s,onClose:n,onSuccess:a}){const{t:r}=M("plugin"),[l,c]=m.useState(!0),[o,u]=m.useState(!1),[x,i]=m.useState(null),d=ko({config:To(Do())}),f=fe({resolver:ye(d),defaultValues:{config:{}}});m.useEffect(()=>{(async()=>{try{const{data:p}=await ys.getPluginConfig(s);i(p),f.reset({config:Object.fromEntries(Object.entries(p).map(([g,j])=>[g,j.value]))})}catch{A.error(r("messages.configLoadError"))}finally{c(!1)}})()},[s]);const w=async C=>{u(!0);try{await ys.updatePluginConfig(s,C.config),A.success(r("messages.configSaveSuccess")),a()}catch{A.error(r("messages.configSaveError"))}finally{u(!1)}},P=(C,p)=>{switch(p.type){case"string":return e.jsx(b,{control:f.control,name:`config.${C}`,render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{children:p.label||p.description}),e.jsx(N,{children:e.jsx(T,{placeholder:p.placeholder,...g})}),p.description&&p.label&&e.jsx("p",{className:"text-sm text-muted-foreground",children:p.description}),e.jsx(k,{})]})},C);case"number":case"percentage":return e.jsx(b,{control:f.control,name:`config.${C}`,render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{children:p.label||p.description}),e.jsx(N,{children:e.jsxs("div",{className:"relative",children:[e.jsx(T,{type:"number",placeholder:p.placeholder,...g,onChange:j=>{const S=Number(j.target.value);p.type==="percentage"?g.onChange(Math.min(100,Math.max(0,S))):g.onChange(S)},className:p.type==="percentage"?"pr-8":"",min:p.type==="percentage"?0:void 0,max:p.type==="percentage"?100:void 0,step:p.type==="percentage"?1:void 0}),p.type==="percentage"&&e.jsx("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3",children:e.jsx(Po,{className:"h-4 w-4 text-muted-foreground"})})]})}),p.description&&p.label&&e.jsx("p",{className:"text-sm text-muted-foreground",children:p.description}),e.jsx(k,{})]})},C);case"select":return e.jsx(b,{control:f.control,name:`config.${C}`,render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{children:p.label||p.description}),e.jsxs(J,{onValueChange:g.onChange,defaultValue:g.value,children:[e.jsx(N,{children:e.jsx(W,{children:e.jsx(Z,{placeholder:p.placeholder})})}),e.jsx(Y,{children:p.options?.map(j=>e.jsx(q,{value:j.value,children:j.label},j.value))})]}),p.description&&p.label&&e.jsx("p",{className:"text-sm text-muted-foreground",children:p.description}),e.jsx(k,{})]})},C);case"boolean":return e.jsx(b,{control:f.control,name:`config.${C}`,render:({field:g})=>e.jsxs(v,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(y,{className:"text-base",children:p.label||p.description}),p.description&&p.label&&e.jsx("p",{className:"text-sm text-muted-foreground",children:p.description})]}),e.jsx(N,{children:e.jsx(Q,{checked:g.value,onCheckedChange:g.onChange})})]})},C);case"text":return e.jsx(b,{control:f.control,name:`config.${C}`,render:({field:g})=>e.jsxs(v,{children:[e.jsx(y,{children:p.label||p.description}),e.jsx(N,{children:e.jsx(bs,{placeholder:p.placeholder,...g})}),p.description&&p.label&&e.jsx("p",{className:"text-sm text-muted-foreground",children:p.description}),e.jsx(k,{})]})},C);default:return null}};return l?e.jsxs("div",{className:"space-y-4",children:[e.jsx(oe,{className:"h-4 w-[200px]"}),e.jsx(oe,{className:"h-10 w-full"}),e.jsx(oe,{className:"h-4 w-[200px]"}),e.jsx(oe,{className:"h-10 w-full"})]}):e.jsx(Ne,{...f,children:e.jsxs("form",{onSubmit:f.handleSubmit(w),className:"space-y-4",children:[x&&Object.entries(x).map(([C,p])=>P(C,p)),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(D,{type:"button",variant:"outline",onClick:n,disabled:o,children:r("config.cancel")}),e.jsx(D,{type:"submit",loading:o,disabled:o,children:r("config.save")})]})]})})}function Tm(){const{t:s}=M("plugin"),[n,a]=m.useState(null),[r,l]=m.useState(!1),[c,o]=m.useState(null),[u,x]=m.useState(""),[i,d]=m.useState("all"),[f,w]=m.useState(!1),[P,C]=m.useState(!1),[p,g]=m.useState(!1),j=m.useRef(null),{data:S,isLoading:R,refetch:E}=ie({queryKey:["pluginList"],queryFn:async()=>{const{data:I}=await ys.getPluginList();return I}});S&&[...new Set(S.map(I=>I.category||"other"))];const F=S?.filter(I=>{const B=I.name.toLowerCase().includes(u.toLowerCase())||I.description.toLowerCase().includes(u.toLowerCase())||I.code.toLowerCase().includes(u.toLowerCase()),ne=i==="all"||I.category===i;return B&&ne}),H=async I=>{a(I),ys.installPlugin(I).then(()=>{A.success(s("messages.installSuccess")),E()}).catch(B=>{A.error(B.message||s("messages.installError"))}).finally(()=>{a(null)})},te=async I=>{a(I),ys.uninstallPlugin(I).then(()=>{A.success(s("messages.uninstallSuccess")),E()}).catch(B=>{A.error(B.message||s("messages.uninstallError"))}).finally(()=>{a(null)})},O=async(I,B)=>{a(I),(B?ys.disablePlugin:ys.enablePlugin)(I).then(()=>{A.success(s(B?"messages.disableSuccess":"messages.enableSuccess")),E()}).catch(Qe=>{A.error(Qe.message||s(B?"messages.disableError":"messages.enableError"))}).finally(()=>{a(null)})},se=I=>{S?.find(B=>B.code===I),o(I),l(!0)},He=async I=>{if(!I.name.endsWith(".zip")){A.error(s("upload.error.format"));return}w(!0),ys.uploadPlugin(I).then(()=>{A.success(s("messages.uploadSuccess")),C(!1),E()}).catch(B=>{A.error(B.message||s("messages.uploadError"))}).finally(()=>{w(!1),j.current&&(j.current.value="")})},G=I=>{I.preventDefault(),I.stopPropagation(),I.type==="dragenter"||I.type==="dragover"?g(!0):I.type==="dragleave"&&g(!1)},ae=I=>{I.preventDefault(),I.stopPropagation(),g(!1),I.dataTransfer.files&&I.dataTransfer.files[0]&&He(I.dataTransfer.files[0])},$=async I=>{a(I),ys.deletePlugin(I).then(()=>{A.success(s("messages.deleteSuccess")),E()}).catch(B=>{A.error(B.message||s("messages.deleteError"))}).finally(()=>{a(null)})};return e.jsxs(Pe,{children:[e.jsxs(Ee,{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(Ta,{className:"h-6 w-6"}),e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:s("title")})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{children:[e.jsxs("div",{className:"mb-8 space-y-4",children:[e.jsxs("div",{className:"flex flex-col gap-4 md:flex-row md:items-center md:justify-between",children:[e.jsxs("div",{className:"relative max-w-sm flex-1",children:[e.jsx(An,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),e.jsx(T,{placeholder:s("search.placeholder"),value:u,onChange:I=>x(I.target.value),className:"pl-9"})]}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs(D,{onClick:()=>C(!0),variant:"outline",className:"shrink-0",size:"sm",children:[e.jsx(pt,{className:"mr-2 h-4 w-4"}),s("upload.button")]})})]}),e.jsxs(Wt,{defaultValue:"all",className:"w-full",children:[e.jsxs(bt,{children:[e.jsx(Ke,{value:"all",children:s("tabs.all")}),e.jsx(Ke,{value:"installed",children:s("tabs.installed")}),e.jsx(Ke,{value:"available",children:s("tabs.available")})]}),e.jsx(Ns,{value:"all",className:"mt-6",children:e.jsx("div",{className:"space-y-4",children:R?e.jsxs(e.Fragment,{children:[e.jsx(ca,{}),e.jsx(ca,{}),e.jsx(ca,{})]}):F?.map(I=>e.jsx(oa,{plugin:I,onInstall:H,onUninstall:te,onToggleEnable:O,onOpenConfig:se,onDelete:$,isLoading:n===I.name},I.name))})}),e.jsx(Ns,{value:"installed",className:"mt-6",children:e.jsx("div",{className:"space-y-4",children:F?.filter(I=>I.is_installed).map(I=>e.jsx(oa,{plugin:I,onInstall:H,onUninstall:te,onToggleEnable:O,onOpenConfig:se,onDelete:$,isLoading:n===I.name},I.name))})}),e.jsx(Ns,{value:"available",className:"mt-6",children:e.jsx("div",{className:"space-y-4",children:F?.filter(I=>!I.is_installed).map(I=>e.jsx(oa,{plugin:I,onInstall:H,onUninstall:te,onToggleEnable:O,onOpenConfig:se,onDelete:$,isLoading:n===I.name},I.code))})})]})]}),e.jsx(ve,{open:r,onOpenChange:l,children:e.jsxs(pe,{className:"sm:max-w-lg",children:[e.jsxs(we,{children:[e.jsxs(be,{children:[S?.find(I=>I.code===c)?.name," ",s("config.title")]}),e.jsx(ke,{children:s("config.description")})]}),c&&e.jsx(km,{pluginName:c,onClose:()=>l(!1),onSuccess:()=>{l(!1),E()}})]})}),e.jsx(ve,{open:P,onOpenChange:C,children:e.jsxs(pe,{className:"sm:max-w-md",children:[e.jsxs(we,{children:[e.jsx(be,{children:s("upload.title")}),e.jsx(ke,{children:s("upload.description")})]}),e.jsxs("div",{className:_("relative mt-4 flex h-64 flex-col items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/25 px-5 py-10 text-center transition-colors",p&&"border-primary/50 bg-muted/50"),onDragEnter:G,onDragLeave:G,onDragOver:G,onDrop:ae,children:[e.jsx("input",{type:"file",ref:j,className:"hidden",accept:".zip",onChange:I=>{const B=I.target.files?.[0];B&&He(B)}}),f?e.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[e.jsx("div",{className:"h-10 w-10 animate-spin rounded-full border-b-2 border-primary"}),e.jsx("div",{className:"text-sm text-muted-foreground",children:s("upload.uploading")})]}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsx("div",{className:"rounded-full border-2 border-muted-foreground/25 p-3",children:e.jsx(pt,{className:"h-6 w-6 text-muted-foreground/50"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-sm font-medium",children:[s("upload.dragText")," ",e.jsx("button",{type:"button",onClick:()=>j.current?.click(),className:"mx-1 text-primary hover:underline",children:s("upload.clickText")})]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s("upload.supportText")})]})]})})]})]})})]})]})}function oa({plugin:s,onInstall:n,onUninstall:a,onToggleEnable:r,onOpenConfig:l,onDelete:c,isLoading:o}){const{t:u}=M("plugin");return e.jsxs(Be,{className:"group relative overflow-hidden transition-all hover:shadow-md",children:[e.jsxs(es,{children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ws,{children:s.name}),s.is_installed&&e.jsx(K,{variant:s.is_enabled?"success":"secondary",children:s.is_enabled?u("status.enabled"):u("status.disabled")})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(Ta,{className:"h-4 w-4"}),e.jsx("code",{className:"rounded bg-muted px-1 py-0.5",children:s.code})]}),e.jsxs("div",{children:["v",s.version]})]})]})}),e.jsx(Zs,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"mt-2",children:s.description}),e.jsx("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:e.jsxs("div",{className:"flex items-center gap-1",children:[u("author"),": ",s.author]})})]})})]}),e.jsx(ss,{children:e.jsx("div",{className:"flex items-center justify-end space-x-2",children:s.is_installed?e.jsxs(e.Fragment,{children:[e.jsxs(D,{variant:"outline",size:"sm",onClick:()=>l(s.code),disabled:!s.is_enabled||o,children:[e.jsx(Eo,{className:"mr-2 h-4 w-4"}),u("button.config")]}),e.jsxs(D,{variant:s.is_enabled?"destructive":"default",size:"sm",onClick:()=>r(s.code,s.is_enabled),disabled:o,children:[e.jsx(Ro,{className:"mr-2 h-4 w-4"}),s.is_enabled?u("button.disable"):u("button.enable")]}),e.jsx(We,{title:u("uninstall.title"),description:u("uninstall.description"),cancelText:u("common:cancel"),confirmText:u("uninstall.button"),variant:"destructive",onConfirm:()=>a(s.code),children:e.jsxs(D,{variant:"outline",size:"sm",className:"text-muted-foreground hover:text-destructive",disabled:o,children:[e.jsx(os,{className:"mr-2 h-4 w-4"}),u("button.uninstall")]})})]}):e.jsxs(e.Fragment,{children:[e.jsx(D,{onClick:()=>n(s.code),disabled:o,loading:o,children:u("button.install")}),e.jsx(We,{title:u("delete.title"),description:u("delete.description"),cancelText:u("common:cancel"),confirmText:u("delete.button"),variant:"destructive",onConfirm:()=>c(s.code),children:e.jsx(D,{variant:"ghost",size:"icon",className:"h-8 w-8 text-muted-foreground hover:text-destructive",disabled:o,children:e.jsx(os,{className:"h-4 w-4"})})})]})})})]})}function ca(){return e.jsxs(Be,{children:[e.jsxs(es,{children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(oe,{className:"h-6 w-[200px]"}),e.jsx(oe,{className:"h-6 w-[80px]"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(oe,{className:"h-5 w-[120px]"}),e.jsx(oe,{className:"h-5 w-[60px]"})]})]})}),e.jsxs("div",{className:"space-y-2 pt-2",children:[e.jsx(oe,{className:"h-4 w-[300px]"}),e.jsx(oe,{className:"h-4 w-[150px]"})]})]}),e.jsx(ss,{children:e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(oe,{className:"h-9 w-[100px]"}),e.jsx(oe,{className:"h-9 w-[100px]"}),e.jsx(oe,{className:"h-8 w-8"})]})})]})}const Dm=Object.freeze(Object.defineProperty({__proto__:null,default:Tm},Symbol.toStringTag,{value:"Module"})),Pm=(s,n)=>{let a=null;switch(s.field_type){case"input":a=e.jsx(T,{placeholder:s.placeholder,...n});break;case"textarea":a=e.jsx(bs,{placeholder:s.placeholder,...n});break;case"select":a=e.jsx("select",{className:_(Xs({variant:"outline"}),"w-full appearance-none font-normal"),...n,children:s.select_options&&Object.keys(s.select_options).map(r=>e.jsx("option",{value:r,children:s.select_options?.[r]},r))});break;default:a=null;break}return a};function Em({themeKey:s,themeInfo:n}){const{t:a}=M("theme"),[r,l]=m.useState(!1),[c,o]=m.useState(!1),[u,x]=m.useState(!1),i=fe({defaultValues:n.configs.reduce((w,P)=>(w[P.field_name]="",w),{})}),d=async()=>{o(!0),Nc(s).then(({data:w})=>{Object.entries(w).forEach(([P,C])=>{i.setValue(P,C)})}).finally(()=>{o(!1)})},f=async w=>{x(!0),_c(s,w).then(()=>{A.success(a("config.success")),l(!1)}).finally(()=>{x(!1)})};return e.jsxs(ve,{open:r,onOpenChange:w=>{l(w),w?d():i.reset()},children:[e.jsx(Ye,{asChild:!0,children:e.jsx(D,{variant:"outline",children:a("card.configureTheme")})}),e.jsxs(pe,{className:"max-h-[90vh] overflow-auto sm:max-w-[425px]",children:[e.jsxs(we,{children:[e.jsx(be,{children:a("config.title",{name:n.name})}),e.jsx(ke,{children:a("config.description")})]}),c?e.jsx("div",{className:"flex h-40 items-center justify-center",children:e.jsx(Pa,{className:"h-6 w-6 animate-spin"})}):e.jsx(Ne,{...i,children:e.jsxs("form",{onSubmit:i.handleSubmit(f),className:"space-y-4",children:[n.configs.map(w=>e.jsx(b,{control:i.control,name:w.field_name,render:({field:P})=>e.jsxs(v,{children:[e.jsx(y,{children:w.label}),e.jsx(N,{children:Pm(w,P)}),e.jsx(k,{})]})},w.field_name)),e.jsxs(Oe,{className:"mt-6 gap-2",children:[e.jsx(D,{type:"button",variant:"secondary",onClick:()=>l(!1),children:a("config.cancel")}),e.jsx(D,{type:"submit",loading:u,children:a("config.save")})]})]})})]})]})}function Rm(){const{t:s}=M("theme"),[n,a]=m.useState(null),[r,l]=m.useState(!1),[c,o]=m.useState(!1),[u,x]=m.useState(!1),[i,d]=m.useState(null),f=m.useRef(null),[w,P]=m.useState(0),{data:C,isLoading:p,refetch:g}=ie({queryKey:["themeList"],queryFn:async()=>{const{data:O}=await yc();return O}}),j=async O=>{a(O),Sc({frontend_theme:O}).then(()=>{A.success("主题切换成功"),g()}).finally(()=>{a(null)})},S=async O=>{if(!O.name.endsWith(".zip")){A.error(s("upload.error.format"));return}l(!0),wc(O).then(()=>{A.success("主题上传成功"),o(!1),g()}).finally(()=>{l(!1),f.current&&(f.current.value="")})},R=O=>{O.preventDefault(),O.stopPropagation(),O.type==="dragenter"||O.type==="dragover"?x(!0):O.type==="dragleave"&&x(!1)},E=O=>{O.preventDefault(),O.stopPropagation(),x(!1),O.dataTransfer.files&&O.dataTransfer.files[0]&&S(O.dataTransfer.files[0])},F=()=>{i&&P(O=>O===0?i.images.length-1:O-1)},H=()=>{i&&P(O=>O===i.images.length-1?0:O+1)},te=(O,se)=>{P(0),d({name:O,images:se})};return e.jsxs(Pe,{children:[e.jsxs(Ee,{className:"flex items-center justify-between",children:[e.jsx(qe,{}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"",children:[e.jsxs("header",{className:"mb-8",children:[e.jsx("div",{className:"mb-2",children:e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:s("title")})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-muted-foreground",children:s("description")}),e.jsxs(D,{onClick:()=>o(!0),variant:"outline",className:"ml-4 shrink-0",size:"sm",children:[e.jsx(pt,{className:"mr-2 h-4 w-4"}),s("upload.button")]})]})]}),e.jsx("section",{className:"grid gap-6 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3",children:p?e.jsxs(e.Fragment,{children:[e.jsx(cn,{}),e.jsx(cn,{})]}):C?.themes&&Object.entries(C.themes).map(([O,se])=>e.jsx(Be,{className:"group relative overflow-hidden transition-all hover:shadow-md",style:{backgroundImage:se.background_url?`url(${se.background_url})`:"none",backgroundSize:"cover",backgroundPosition:"center"},children:e.jsxs("div",{className:_("relative z-10 h-full transition-colors",se.background_url?"group-hover:from-background/98 bg-gradient-to-t from-background/95 via-background/80 to-background/60 backdrop-blur-[1px] group-hover:via-background/90 group-hover:to-background/70":"bg-background"),children:[!!se.can_delete&&e.jsx("div",{className:"absolute right-2 top-2",children:e.jsx(We,{title:s("card.delete.title"),description:s("card.delete.description"),confirmText:s("card.delete.button"),variant:"destructive",onConfirm:async()=>{if(O===C?.active){A.error(s("card.delete.error.active"));return}a(O),Cc(O).then(()=>{A.success("主题删除成功"),g()}).finally(()=>{a(null)})},children:e.jsx(D,{disabled:n===O,loading:n===O,variant:"ghost",size:"icon",className:"h-8 w-8 text-muted-foreground hover:text-destructive",children:e.jsx(os,{className:"h-4 w-4"})})})}),e.jsxs(es,{children:[e.jsx(ws,{children:se.name}),e.jsx(Zs,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{children:se.description}),se.version&&e.jsx("div",{className:"text-sm text-muted-foreground",children:s("card.version",{version:se.version})})]})})]}),e.jsxs(ss,{className:"flex items-center justify-end space-x-3",children:[se.images&&Array.isArray(se.images)&&se.images.length>0&&e.jsx(D,{variant:"outline",size:"icon",className:"h-8 w-8",onClick:()=>te(se.name,se.images),children:e.jsx(Io,{className:"h-4 w-4"})}),e.jsx(Em,{themeKey:O,themeInfo:se}),e.jsx(D,{onClick:()=>j(O),disabled:n===O||O===C.active,loading:n===O,variant:O===C.active?"secondary":"default",children:O===C.active?s("card.currentTheme"):s("card.activateTheme")})]})]})},O))}),e.jsx(ve,{open:c,onOpenChange:o,children:e.jsxs(pe,{className:"sm:max-w-md",children:[e.jsxs(we,{children:[e.jsx(be,{children:s("upload.title")}),e.jsx(ke,{children:s("upload.description")})]}),e.jsxs("div",{className:_("relative mt-4 flex h-64 flex-col items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/25 px-5 py-10 text-center transition-colors",u&&"border-primary/50 bg-muted/50"),onDragEnter:R,onDragLeave:R,onDragOver:R,onDrop:E,children:[e.jsx("input",{type:"file",ref:f,className:"hidden",accept:".zip",onChange:O=>{const se=O.target.files?.[0];se&&S(se)}}),r?e.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[e.jsx("div",{className:"h-10 w-10 animate-spin rounded-full border-b-2 border-primary"}),e.jsx("div",{className:"text-sm text-muted-foreground",children:s("upload.uploading")})]}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsx("div",{className:"rounded-full border-2 border-muted-foreground/25 p-3",children:e.jsx(pt,{className:"h-6 w-6 text-muted-foreground/50"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-sm font-medium",children:[s("upload.dragText")," ",e.jsx("button",{type:"button",onClick:()=>f.current?.click(),className:"mx-1 text-primary hover:underline",children:s("upload.clickText")})]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s("upload.supportText")})]})]})})]})]})}),e.jsx(ve,{open:!!i,onOpenChange:O=>{O||(d(null),P(0))},children:e.jsxs(pe,{className:"max-w-4xl",children:[e.jsxs(we,{children:[e.jsxs(be,{children:[i?.name," ",s("preview.title")]}),e.jsx(ke,{className:"text-center",children:i&&s("preview.imageCount",{current:w+1,total:i.images.length})})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"aspect-[16/9] overflow-hidden rounded-lg border bg-muted",children:i?.images[w]&&e.jsx("img",{src:i.images[w],alt:`${i.name} 预览图 ${w+1}`,className:"h-full w-full object-contain"})}),i&&i.images.length>1&&e.jsxs(e.Fragment,{children:[e.jsx(D,{variant:"outline",size:"icon",className:"absolute left-4 top-1/2 h-8 w-8 -translate-y-1/2 rounded-full bg-background/80 hover:bg-background",onClick:F,children:e.jsx(Vo,{className:"h-4 w-4"})}),e.jsx(D,{variant:"outline",size:"icon",className:"absolute right-4 top-1/2 h-8 w-8 -translate-y-1/2 rounded-full bg-background/80 hover:bg-background",onClick:H,children:e.jsx(Mo,{className:"h-4 w-4"})})]})]}),i&&i.images.length>1&&e.jsx("div",{className:"mt-4 flex gap-2 overflow-x-auto pb-2",children:i.images.map((O,se)=>e.jsx("button",{onClick:()=>P(se),className:_("relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border-2",w===se?"border-primary":"border-transparent"),children:e.jsx("img",{src:O,alt:`缩略图 ${se+1}`,className:"h-full w-full object-cover"})},se))})]})})]})]})}function cn(){return e.jsxs(Be,{children:[e.jsxs(es,{children:[e.jsx(oe,{className:"h-6 w-[200px]"}),e.jsx(oe,{className:"h-4 w-[300px]"})]}),e.jsxs(ss,{className:"flex items-center justify-end space-x-3",children:[e.jsx(oe,{className:"h-10 w-[100px]"}),e.jsx(oe,{className:"h-10 w-[100px]"})]})]})}const Im=Object.freeze(Object.defineProperty({__proto__:null,default:Rm},Symbol.toStringTag,{value:"Module"})),La=m.forwardRef(({className:s,value:n,onChange:a,...r},l)=>{const[c,o]=m.useState("");m.useEffect(()=>{if(c.includes(",")){const x=new Set([...n,...c.split(",").map(i=>i.trim())]);a(Array.from(x)),o("")}},[c,a,n]);const u=()=>{if(c){const x=new Set([...n,c]);a(Array.from(x)),o("")}};return e.jsxs("div",{className:_(" has-[:focus-visible]:outline-none has-[:focus-visible]:ring-1 has-[:focus-visible]:ring-neutral-950  dark:has-[:focus-visible]:ring-neutral-300  flex w-full flex-wrap gap-2 rounded-md border border-input shadow-sm px-3 py-2 text-sm ring-offset-white  disabled:cursor-not-allowed disabled:opacity-50",s),children:[n.map(x=>e.jsxs(K,{variant:"secondary",children:[x,e.jsx(X,{variant:"ghost",size:"icon",className:"ml-2 h-3 w-3",onClick:()=>{a(n.filter(i=>i!==x))},children:e.jsx(ja,{className:"w-3"})})]},x)),e.jsx("input",{className:"flex-1 outline-none placeholder:text-muted-foreground bg-transparent",value:c,onChange:x=>o(x.target.value),onKeyDown:x=>{x.key==="Enter"||x.key===","?(x.preventDefault(),u()):x.key==="Backspace"&&c.length===0&&n.length>0&&(x.preventDefault(),a(n.slice(0,-1)))},...r,ref:l})]})});La.displayName="InputTags";const Vm=h.object({id:h.number().nullable(),title:h.string().min(1).max(250),content:h.string().min(1),show:h.boolean(),tags:h.array(h.string()),img_url:h.string().nullable()}),Mm={id:null,show:!1,tags:[],img_url:"",title:"",content:""};function Wr({refetch:s,dialogTrigger:n,type:a="add",defaultFormValues:r=Mm}){const{t:l}=M("notice"),[c,o]=m.useState(!1),u=fe({resolver:ye(Vm),defaultValues:r,mode:"onChange",shouldFocusError:!0}),x=new Ea({html:!0});return e.jsx(Ne,{...u,children:e.jsxs(ve,{onOpenChange:o,open:c,children:[e.jsx(Ye,{asChild:!0,children:n||e.jsxs(D,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(Re,{icon:"ion:add"})," ",e.jsx("div",{children:l("form.add.button")})]})}),e.jsxs(pe,{className:"sm:max-w-[1025px]",children:[e.jsxs(we,{children:[e.jsx(be,{children:l(a==="add"?"form.add.title":"form.edit.title")}),e.jsx(ke,{})]}),e.jsx(b,{control:u.control,name:"title",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.title.label")}),e.jsx("div",{className:"relative ",children:e.jsx(N,{children:e.jsx(T,{placeholder:l("form.fields.title.placeholder"),...i})})}),e.jsx(k,{})]})}),e.jsx(b,{control:u.control,name:"content",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.content.label")}),e.jsx(N,{children:e.jsx(Ra,{style:{height:"500px"},value:i.value,renderHTML:d=>x.render(d),onChange:({text:d})=>{i.onChange(d)}})}),e.jsx(k,{})]})}),e.jsx(b,{control:u.control,name:"img_url",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.img_url.label")}),e.jsx("div",{className:"relative",children:e.jsx(N,{children:e.jsx(T,{type:"text",placeholder:l("form.fields.img_url.placeholder"),...i,value:i.value||""})})}),e.jsx(k,{})]})}),e.jsx(b,{control:u.control,name:"show",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.show.label")}),e.jsx("div",{className:"relative py-2",children:e.jsx(N,{children:e.jsx(Q,{checked:i.value,onCheckedChange:i.onChange})})}),e.jsx(k,{})]})}),e.jsx(b,{control:u.control,name:"tags",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.fields.tags.label")}),e.jsx(N,{children:e.jsx(La,{value:i.value,onChange:i.onChange,placeholder:l("form.fields.tags.placeholder"),className:"w-full"})}),e.jsx(k,{})]})}),e.jsxs(Oe,{children:[e.jsx(Nt,{asChild:!0,children:e.jsx(D,{type:"button",variant:"outline",children:l("form.buttons.cancel")})}),e.jsx(D,{type:"submit",onClick:i=>{i.preventDefault(),u.handleSubmit(async d=>{Hc(d).then(({data:f})=>{f&&(A.success(l("form.buttons.success")),s(),o(!1))})})()},children:l("form.buttons.submit")})]})]})]})})}function Fm({table:s,refetch:n,saveOrder:a,isSortMode:r}){const{t:l}=M("notice"),c=s.getState().columnFilters.length>0;return e.jsxs("div",{className:"flex items-center justify-between space-x-2 ",children:[e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[!r&&e.jsx(Wr,{refetch:n}),!r&&e.jsx(T,{placeholder:l("table.toolbar.search"),value:s.getColumn("title")?.getFilterValue()??"",onChange:o=>s.getColumn("title")?.setFilterValue(o.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),c&&!r&&e.jsxs(D,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-9 px-2 lg:px-3",children:[l("table.toolbar.reset"),e.jsx(Xe,{className:"ml-2 h-4 w-4"})]})]}),s.getRowCount()>0&&e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(D,{variant:r?"default":"outline",onClick:a,className:"h-8",size:"sm",children:l(r?"table.toolbar.sort.save":"table.toolbar.sort.edit")})})]})}const Om=s=>{const{t:n}=M("notice");return[{id:"drag-handle",header:"",cell:()=>e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(Fo,{className:"h-4 w-4 text-muted-foreground cursor-move"})}),size:40,enableSorting:!1},{accessorKey:"id",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.id")}),cell:({row:a})=>e.jsx(K,{variant:"outline",className:"font-mono",children:a.getValue("id")}),enableSorting:!0,size:60},{accessorKey:"show",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.show")}),cell:({row:a})=>e.jsx("div",{className:"flex items-center",children:e.jsx(Q,{defaultChecked:a.getValue("show"),onCheckedChange:async()=>{const{data:r}=await Kc({id:a.original.id});r||s()}})}),enableSorting:!1,size:100},{accessorKey:"title",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.title")}),cell:({row:a})=>e.jsx("div",{className:"flex max-w-[500px] items-center",children:e.jsx("span",{className:"truncate font-medium",children:a.getValue("title")})}),enableSorting:!1,size:6e3},{id:"actions",header:({column:a})=>e.jsx(L,{className:"justify-end",column:a,title:n("table.columns.actions")}),cell:({row:a})=>e.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[e.jsx(Wr,{refetch:s,dialogTrigger:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:[e.jsx(qs,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:n("table.actions.edit")})]}),type:"edit",defaultFormValues:a.original}),e.jsx(We,{title:n("table.actions.delete.title"),description:n("table.actions.delete.description"),onConfirm:async()=>{Uc({id:a.original.id}).then(()=>{A.success(n("table.actions.delete.success")),s()})},children:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(os,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:n("table.actions.delete.title")})]})})]}),size:100}]};function zm(){const[s,n]=m.useState({}),[a,r]=m.useState({}),[l,c]=m.useState([]),[o,u]=m.useState([]),[x,i]=m.useState(!1),[d,f]=m.useState({}),[w,P]=m.useState({pageSize:50,pageIndex:0}),[C,p]=m.useState([]),{refetch:g}=ie({queryKey:["notices"],queryFn:async()=>{const{data:F}=await nn.getList();return p(F),F}});m.useEffect(()=>{r({"drag-handle":x,content:!x,created_at:!x,actions:!x}),P({pageSize:x?99999:50,pageIndex:0})},[x]);const j=(F,H)=>{x&&(F.dataTransfer.setData("text/plain",H.toString()),F.currentTarget.classList.add("opacity-50"))},S=(F,H)=>{if(!x)return;F.preventDefault(),F.currentTarget.classList.remove("bg-muted");const te=parseInt(F.dataTransfer.getData("text/plain"));if(te===H)return;const O=[...C],[se]=O.splice(te,1);O.splice(H,0,se),p(O)},R=async()=>{if(!x){i(!0);return}nn.sort(C.map(F=>F.id)).then(()=>{A.success("排序保存成功"),i(!1),g()}).finally(()=>{i(!1)})},E=ts({data:C??[],columns:Om(g),state:{sorting:o,columnVisibility:a,rowSelection:s,columnFilters:l,columnSizing:d,pagination:w},enableRowSelection:!0,onRowSelectionChange:n,onSortingChange:u,onColumnFiltersChange:c,onColumnVisibilityChange:r,onColumnSizingChange:f,onPaginationChange:P,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),getSortedRowModel:us(),getFacetedRowModel:Ts(),getFacetedUniqueValues:Ds(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx("div",{className:"space-y-4",children:e.jsx(xs,{table:E,toolbar:F=>e.jsx(Fm,{table:F,refetch:g,saveOrder:R,isSortMode:x}),draggable:x,onDragStart:j,onDragEnd:F=>F.currentTarget.classList.remove("opacity-50"),onDragOver:F=>{F.preventDefault(),F.currentTarget.classList.add("bg-muted")},onDragLeave:F=>F.currentTarget.classList.remove("bg-muted"),onDrop:S,showPagination:!x})})}function Lm(){const{t:s}=M("notice");return e.jsxs(Pe,{children:[e.jsxs(Ee,{className:"flex items-center justify-between",children:[e.jsx(qe,{}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("div",{className:"mb-2",children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")})}),e.jsx("p",{className:"text-muted-foreground",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(zm,{})})]})]})}const Am=Object.freeze(Object.defineProperty({__proto__:null,default:Lm},Symbol.toStringTag,{value:"Module"})),$m=h.object({id:h.number().nullable(),language:h.string().max(250),category:h.string().max(250),title:h.string().min(1).max(250),body:h.string().min(1),show:h.boolean()}),qm={id:null,language:"zh-CN",category:"",title:"",body:"",show:!1};function Yr({refreshData:s,dialogTrigger:n,type:a="add",defaultFormValues:r=qm}){const{t:l}=M("knowledge"),[c,o]=m.useState(!1),u=fe({resolver:ye($m),defaultValues:r,mode:"onChange",shouldFocusError:!0}),x=new Ea({html:!0});return m.useEffect(()=>{c&&r.id&&Gc(r.id).then(({data:i})=>{u.reset(i)})},[r.id,u,c]),e.jsxs(ve,{onOpenChange:o,open:c,children:[e.jsx(Ye,{asChild:!0,children:n||e.jsxs(D,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(Re,{icon:"ion:add"})," ",e.jsx("div",{children:l("form.add")})]})}),e.jsxs(pe,{className:"sm:max-w-[1025px]",children:[e.jsxs(we,{children:[e.jsx(be,{children:l(a==="add"?"form.add":"form.edit")}),e.jsx(ke,{})]}),e.jsxs(Ne,{...u,children:[e.jsx(b,{control:u.control,name:"title",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.title")}),e.jsx("div",{className:"relative ",children:e.jsx(N,{children:e.jsx(T,{placeholder:l("form.titlePlaceholder"),...i})})}),e.jsx(k,{})]})}),e.jsx(b,{control:u.control,name:"category",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.category")}),e.jsx("div",{className:"relative ",children:e.jsx(N,{children:e.jsx(T,{placeholder:l("form.categoryPlaceholder"),...i})})}),e.jsx(k,{})]})}),e.jsx(b,{control:u.control,name:"language",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.language")}),e.jsx(N,{children:e.jsxs(J,{value:i.value,onValueChange:i.onChange,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:l("form.languagePlaceholder")})}),e.jsx(Y,{children:[{value:"en-US"},{value:"ja-JP"},{value:"ko-KR"},{value:"vi-VN"},{value:"zh-CN"},{value:"zh-TW"}].map(d=>e.jsx(q,{value:d.value,className:"cursor-pointer",children:l(`languages.${d.value}`)},d.value))})]})})]})}),e.jsx(b,{control:u.control,name:"body",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.content")}),e.jsx(N,{children:e.jsx(Ra,{style:{height:"500px"},value:i.value,renderHTML:d=>x.render(d),onChange:({text:d})=>{i.onChange(d)}})}),e.jsx(k,{})]})}),e.jsx(b,{control:u.control,name:"show",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.show")}),e.jsx("div",{className:"relative py-2",children:e.jsx(N,{children:e.jsx(Q,{checked:i.value,onCheckedChange:i.onChange})})}),e.jsx(k,{})]})}),e.jsxs(Oe,{children:[e.jsx(Nt,{asChild:!0,children:e.jsx(D,{type:"button",variant:"outline",children:l("form.cancel")})}),e.jsx(D,{type:"submit",onClick:()=>{u.handleSubmit(i=>{Wc(i).then(({data:d})=>{d&&(u.reset(),A.success(l("messages.operationSuccess")),o(!1),s())})})()},children:l("form.submit")})]})]})]})]})}function Hm({column:s,title:n,options:a}){const r=s?.getFacetedUniqueValues(),l=new Set(s?.getFilterValue());return e.jsxs(js,{children:[e.jsx(vs,{asChild:!0,children:e.jsxs(D,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(vt,{className:"mr-2 h-4 w-4"}),n,l?.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(Se,{orientation:"vertical",className:"mx-2 h-4"}),e.jsx(K,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:l.size}),e.jsx("div",{className:"hidden space-x-1 lg:flex",children:l.size>2?e.jsxs(K,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[l.size," selected"]}):a.filter(c=>l.has(c.value)).map(c=>e.jsx(K,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:c.label},c.value))})]})]})}),e.jsx(ms,{className:"w-[200px] p-0",align:"start",children:e.jsxs(Vs,{children:[e.jsx(Us,{placeholder:n}),e.jsxs(Ms,{children:[e.jsx(Ks,{children:"No results found."}),e.jsx(Ge,{children:a.map(c=>{const o=l.has(c.value);return e.jsxs(Ie,{onSelect:()=>{o?l.delete(c.value):l.add(c.value);const u=Array.from(l);s?.setFilterValue(u.length?u:void 0)},children:[e.jsx("div",{className:_("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",o?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(As,{className:_("h-4 w-4")})}),c.icon&&e.jsx(c.icon,{className:"mr-2 h-4 w-4 text-muted-foreground"}),e.jsx("span",{children:c.label}),r?.get(c.value)&&e.jsx("span",{className:"ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs",children:r.get(c.value)})]},c.value)})}),l.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(at,{}),e.jsx(Ge,{children:e.jsx(Ie,{onSelect:()=>s?.setFilterValue(void 0),className:"justify-center text-center",children:"Clear filters"})})]})]})]})})]})}function Um({table:s,refetch:n,saveOrder:a,isSortMode:r}){const l=s.getState().columnFilters.length>0,{t:c}=M("knowledge");return e.jsxs("div",{className:"flex items-center justify-between",children:[r?e.jsx("p",{className:"text-sm text-muted-foreground",children:c("toolbar.sortModeHint")}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Yr,{refreshData:n}),e.jsx(T,{placeholder:c("toolbar.searchPlaceholder"),value:s.getColumn("title")?.getFilterValue()??"",onChange:o=>s.getColumn("title")?.setFilterValue(o.target.value),className:"h-8 w-[250px]"}),s.getColumn("category")&&e.jsx(Hm,{column:s.getColumn("category"),title:c("columns.category"),options:Array.from(new Set(s.getCoreRowModel().rows.map(o=>o.getValue("category")))).map(o=>({label:o,value:o}))}),l&&e.jsxs(D,{variant:"ghost",onClick:()=>s.resetColumnFilters(),children:[c("toolbar.reset"),e.jsx(Xe,{className:"ml-2 h-4 w-4"})]})]}),s.getRowCount()>0&&e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(D,{variant:r?"default":"outline",onClick:a,size:"sm",children:c(r?"toolbar.saveSort":"toolbar.editSort")})})]})}const Km=({refetch:s,isSortMode:n=!1})=>{const{t:a}=M("knowledge");return[{id:"drag-handle",header:()=>null,cell:()=>e.jsx("div",{className:n?"cursor-move":"opacity-0",children:e.jsx(Kt,{className:"size-4"})}),size:40,enableSorting:!1},{accessorKey:"id",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.id")}),cell:({row:r})=>e.jsx(K,{variant:"outline",className:"justify-center",children:r.getValue("id")}),enableSorting:!0,size:70},{accessorKey:"show",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.status")}),cell:({row:r})=>e.jsx("div",{className:"flex items-center",children:e.jsx(Q,{defaultChecked:r.getValue("show"),onCheckedChange:async()=>{Qc({id:r.original.id}).then(({data:l})=>{l||s()})}})}),enableSorting:!1,size:100},{accessorKey:"title",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.title")}),cell:({row:r})=>e.jsx("div",{className:"flex space-x-2",children:e.jsx("span",{className:"line-clamp-2 font-medium",children:r.getValue("title")})}),enableSorting:!0,size:600},{accessorKey:"category",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.category")}),cell:({row:r})=>e.jsx(K,{variant:"secondary",className:"max-w-[180px] truncate",children:r.getValue("category")}),enableSorting:!0,size:1800},{id:"actions",header:({column:r})=>e.jsx(L,{className:"justify-end",column:r,title:a("columns.actions")}),cell:({row:r})=>e.jsxs("div",{className:"flex items-center justify-end space-x-1",children:[e.jsx(Yr,{refreshData:s,dialogTrigger:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:[e.jsx(qs,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:a("form.edit")})]}),type:"edit",defaultFormValues:r.original}),e.jsx(We,{title:a("messages.deleteConfirm"),description:a("messages.deleteDescription"),confirmText:a("messages.deleteButton"),variant:"destructive",onConfirm:async()=>{Yc({id:r.original.id}).then(({data:l})=>{l&&(A.success(a("messages.operationSuccess")),s())})},children:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(os,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:a("messages.deleteButton")})]})})]}),size:100}]};function Bm(){const[s,n]=m.useState([]),[a,r]=m.useState([]),[l,c]=m.useState(!1),[o,u]=m.useState([]),[x,i]=m.useState({"drag-handle":!1}),[d,f]=m.useState({pageSize:20,pageIndex:0}),{refetch:w,isLoading:P,data:C}=ie({queryKey:["knowledge"],queryFn:async()=>{const{data:R}=await Bc();return u(R||[]),R}});m.useEffect(()=>{i({"drag-handle":l,actions:!l}),f({pageSize:l?99999:10,pageIndex:0})},[l]);const p=(R,E)=>{l&&(R.dataTransfer.setData("text/plain",E.toString()),R.currentTarget.classList.add("opacity-50"))},g=(R,E)=>{if(!l)return;R.preventDefault(),R.currentTarget.classList.remove("bg-muted");const F=parseInt(R.dataTransfer.getData("text/plain"));if(F===E)return;const H=[...o],[te]=H.splice(F,1);H.splice(E,0,te),u(H)},j=async()=>{l?Jc({ids:o.map(R=>R.id)}).then(()=>{w(),c(!1),A.success("排序保存成功")}):c(!0)},S=ts({data:o,columns:Km({refetch:w,isSortMode:l}),state:{sorting:a,columnFilters:s,columnVisibility:x,pagination:d},onSortingChange:r,onColumnFiltersChange:n,onColumnVisibilityChange:i,onPaginationChange:f,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),getSortedRowModel:us(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(xs,{table:S,toolbar:R=>e.jsx(Um,{table:R,refetch:w,saveOrder:j,isSortMode:l}),draggable:l,onDragStart:p,onDragEnd:R=>R.currentTarget.classList.remove("opacity-50"),onDragOver:R=>{R.preventDefault(),R.currentTarget.classList.add("bg-muted")},onDragLeave:R=>R.currentTarget.classList.remove("bg-muted"),onDrop:g,showPagination:!l})}function Gm(){const{t:s}=M("knowledge");return e.jsxs(Pe,{children:[e.jsxs(Ee,{children:[e.jsx(qe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight mb-2",children:s("title")}),e.jsx("p",{className:"text-muted-foreground",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(Bm,{})})]})]})}const Wm=Object.freeze(Object.defineProperty({__proto__:null,default:Gm},Symbol.toStringTag,{value:"Module"}));function Ym(s,n){const[a,r]=m.useState(s);return m.useEffect(()=>{const l=setTimeout(()=>r(s),n);return()=>{clearTimeout(l)}},[s,n]),a}function da(s,n){if(s.length===0)return{};if(!n)return{"":s};const a={};return s.forEach(r=>{const l=r[n]||"";a[l]||(a[l]=[]),a[l].push(r)}),a}function Qm(s,n){const a=JSON.parse(JSON.stringify(s));for(const[r,l]of Object.entries(a))a[r]=l.filter(c=>!n.find(o=>o.value===c.value));return a}function Jm(s,n){for(const[,a]of Object.entries(s))if(a.some(r=>n.find(l=>l.value===r.value)))return!0;return!1}const Qr=m.forwardRef(({className:s,...n},a)=>Oo(l=>l.filtered.count===0)?e.jsx("div",{ref:a,className:_("py-6 text-center text-sm",s),"cmdk-empty":"",role:"presentation",...n}):null);Qr.displayName="CommandEmpty";const st=m.forwardRef(({value:s,onChange:n,placeholder:a,defaultOptions:r=[],options:l,delay:c,onSearch:o,loadingIndicator:u,emptyIndicator:x,maxSelected:i=Number.MAX_SAFE_INTEGER,onMaxSelected:d,hidePlaceholderWhenSelected:f,disabled:w,groupBy:P,className:C,badgeClassName:p,selectFirstItem:g=!0,creatable:j=!1,triggerSearchOnFocus:S=!1,commandProps:R,inputProps:E,hideClearAllButton:F=!1},H)=>{const te=m.useRef(null),[O,se]=m.useState(!1),He=m.useRef(!1),[G,ae]=m.useState(!1),[$,I]=m.useState(s||[]),[B,ne]=m.useState(da(r,P)),[Qe,nt]=m.useState(""),rt=Ym(Qe,c||500);m.useImperativeHandle(H,()=>({selectedValue:[...$],input:te.current,focus:()=>te.current?.focus()}),[$]);const _t=m.useCallback(ee=>{const de=$.filter(Ae=>Ae.value!==ee.value);I(de),n?.(de)},[n,$]),bl=m.useCallback(ee=>{const de=te.current;de&&((ee.key==="Delete"||ee.key==="Backspace")&&de.value===""&&$.length>0&&($[$.length-1].fixed||_t($[$.length-1])),ee.key==="Escape"&&de.blur())},[_t,$]);m.useEffect(()=>{s&&I(s)},[s]),m.useEffect(()=>{if(!l||o)return;const ee=da(l||[],P);JSON.stringify(ee)!==JSON.stringify(B)&&ne(ee)},[r,l,P,o,B]),m.useEffect(()=>{const ee=async()=>{ae(!0);const Ae=await o?.(rt);ne(da(Ae||[],P)),ae(!1)};(async()=>{!o||!O||(S&&await ee(),rt&&await ee())})()},[rt,P,O,S]);const yl=()=>{if(!j||Jm(B,[{value:Qe,label:Qe}])||$.find(de=>de.value===Qe))return;const ee=e.jsx(Ie,{value:Qe,className:"cursor-pointer",onMouseDown:de=>{de.preventDefault(),de.stopPropagation()},onSelect:de=>{if($.length>=i){d?.($.length);return}nt("");const Ae=[...$,{value:de,label:de}];I(Ae),n?.(Ae)},children:`Create "${Qe}"`});if(!o&&Qe.length>0||o&&rt.length>0&&!G)return ee},Nl=m.useCallback(()=>{if(x)return o&&!j&&Object.keys(B).length===0?e.jsx(Ie,{value:"-",disabled:!0,children:x}):e.jsx(Qr,{children:x})},[j,x,o,B]),_l=m.useMemo(()=>Qm(B,$),[B,$]),wl=m.useCallback(()=>{if(R?.filter)return R.filter;if(j)return(ee,de)=>ee.toLowerCase().includes(de.toLowerCase())?1:-1},[j,R?.filter]),Cl=m.useCallback(()=>{const ee=$.filter(de=>de.fixed);I(ee),n?.(ee)},[n,$]);return e.jsxs(Vs,{...R,onKeyDown:ee=>{bl(ee),R?.onKeyDown?.(ee)},className:_("h-auto overflow-visible bg-transparent",R?.className),shouldFilter:R?.shouldFilter!==void 0?R.shouldFilter:!o,filter:wl(),children:[e.jsx("div",{className:_("rounded-md border border-input text-sm ring-offset-background focus-within:ring-1 focus-within:ring-ring ",{"px-3 py-2":$.length!==0,"cursor-text":!w&&$.length!==0},C),onClick:()=>{w||te.current?.focus()},children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[$.map(ee=>e.jsxs(K,{className:_("data-[disabled]:bg-muted-foreground data-[disabled]:text-muted data-[disabled]:hover:bg-muted-foreground","data-[fixed]:bg-muted-foreground data-[fixed]:text-muted data-[fixed]:hover:bg-muted-foreground",p),"data-fixed":ee.fixed,"data-disabled":w||void 0,children:[ee.label,e.jsx("button",{className:_("ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2",(w||ee.fixed)&&"hidden"),onKeyDown:de=>{de.key==="Enter"&&_t(ee)},onMouseDown:de=>{de.preventDefault(),de.stopPropagation()},onClick:()=>_t(ee),children:e.jsx(ja,{className:"h-3 w-3 text-muted-foreground hover:text-foreground"})})]},ee.value)),e.jsx(Fe.Input,{...E,ref:te,value:Qe,disabled:w,onValueChange:ee=>{nt(ee),E?.onValueChange?.(ee)},onBlur:ee=>{He.current===!1&&se(!1),E?.onBlur?.(ee)},onFocus:ee=>{se(!0),S&&o?.(rt),E?.onFocus?.(ee)},placeholder:f&&$.length!==0?"":a,className:_("flex-1 bg-transparent outline-none placeholder:text-muted-foreground",{"w-full":f,"px-3 py-2":$.length===0,"ml-1":$.length!==0},E?.className)}),e.jsx("button",{type:"button",onClick:Cl,className:_((F||w||$.length<1||$.filter(ee=>ee.fixed).length===$.length)&&"hidden"),children:e.jsx(ja,{})})]})}),e.jsx("div",{className:"relative",children:O&&e.jsx(Ms,{className:"absolute top-1 z-10 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in",onMouseLeave:()=>{He.current=!1},onMouseEnter:()=>{He.current=!0},onMouseUp:()=>{te.current?.focus()},children:G?e.jsx(e.Fragment,{children:u}):e.jsxs(e.Fragment,{children:[Nl(),yl(),!g&&e.jsx(Ie,{value:"-",className:"hidden"}),Object.entries(_l).map(([ee,de])=>e.jsx(Ge,{heading:ee,className:"h-full overflow-auto",children:e.jsx(e.Fragment,{children:de.map(Ae=>e.jsx(Ie,{value:Ae.value,disabled:Ae.disable,onMouseDown:lt=>{lt.preventDefault(),lt.stopPropagation()},onSelect:()=>{if($.length>=i){d?.($.length);return}nt("");const lt=[...$,Ae];I(lt),n?.(lt)},className:_("cursor-pointer",Ae.disable&&"cursor-default text-muted-foreground"),children:Ae.label},Ae.value))})},ee))]})})})]})});st.displayName="MultipleSelector";const Zm=s=>h.object({id:h.number().optional(),name:h.string().min(2,s("messages.nameValidation.min")).max(50,s("messages.nameValidation.max")).regex(/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/,s("messages.nameValidation.pattern"))});function aa({refetch:s,dialogTrigger:n,defaultValues:a={name:""},type:r="add"}){const{t:l}=M("group"),c=fe({resolver:ye(Zm(l)),defaultValues:a,mode:"onChange"}),[o,u]=m.useState(!1),[x,i]=m.useState(!1),d=async f=>{i(!0),Rc(f).then(()=>{A.success(l(r==="edit"?"messages.updateSuccess":"messages.createSuccess")),s&&s(),c.reset(),u(!1)}).finally(()=>{i(!1)})};return e.jsxs(ve,{open:o,onOpenChange:u,children:[e.jsx(Ye,{asChild:!0,children:n||e.jsxs(D,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(Re,{icon:"ion:add"}),e.jsx("span",{children:l("form.add")})]})}),e.jsxs(pe,{className:"sm:max-w-[425px]",children:[e.jsxs(we,{children:[e.jsx(be,{children:l(r==="edit"?"form.edit":"form.create")}),e.jsx(ke,{children:l(r==="edit"?"form.editDescription":"form.createDescription")})]}),e.jsx(Ne,{...c,children:e.jsxs("form",{onSubmit:c.handleSubmit(d),className:"space-y-4",children:[e.jsx(b,{control:c.control,name:"name",render:({field:f})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.name")}),e.jsx(N,{children:e.jsx(T,{placeholder:l("form.namePlaceholder"),...f,className:"w-full"})}),e.jsx(z,{children:l("form.nameDescription")}),e.jsx(k,{})]})}),e.jsxs(Oe,{className:"gap-2",children:[e.jsx(Nt,{asChild:!0,children:e.jsx(D,{type:"button",variant:"outline",children:l("form.cancel")})}),e.jsxs(D,{type:"submit",disabled:x||!c.formState.isValid,children:[x&&e.jsx(Pa,{className:"mr-2 h-4 w-4 animate-spin"}),l(r==="edit"?"form.update":"form.create")]})]})]})})]})]})}const Jr=m.createContext(void 0);function Xm({children:s,refetch:n}){const[a,r]=m.useState(!1),[l,c]=m.useState(null),[o,u]=m.useState(Te.Shadowsocks);return e.jsx(Jr.Provider,{value:{isOpen:a,setIsOpen:r,editingServer:l,setEditingServer:c,serverType:o,setServerType:u,refetch:n},children:s})}function Zr(){const s=m.useContext(Jr);if(s===void 0)throw new Error("useServerEdit must be used within a ServerEditProvider");return s}function ua({dialogTrigger:s,value:n,setValue:a,templateType:r}){const{t:l}=M("server");m.useEffect(()=>{console.log(n)},[n]);const[c,o]=m.useState(!1),[u,x]=m.useState(()=>{if(!n||Object.keys(n).length===0)return"";try{return JSON.stringify(n,null,2)}catch{return""}}),[i,d]=m.useState(null),f=j=>{if(!j)return null;try{const S=JSON.parse(j);return typeof S!="object"||S===null?l("network_settings.validation.must_be_object"):null}catch{return l("network_settings.validation.invalid_json")}},w={tcp:{label:"TCP",content:{acceptProxyProtocol:!1,header:{type:"none"}}},"tcp-http":{label:"TCP + HTTP",content:{acceptProxyProtocol:!1,header:{type:"http",request:{version:"1.1",method:"GET",path:["/"],headers:{Host:["www.example.com"]}},response:{version:"1.1",status:"200",reason:"OK"}}}},grpc:{label:"gRPC",content:{serviceName:"GunService"}},ws:{label:"WebSocket",content:{path:"/",headers:{Host:"v2ray.com"}}},httpupgrade:{label:"HttpUpgrade",content:{acceptProxyProtocol:!1,path:"/",host:"xray.com",headers:{key:"value"}}},xhttp:{label:"XHTTP",content:{host:"example.com",path:"/yourpath",mode:"auto",extra:{headers:{},xPaddingBytes:"100-1000",noGRPCHeader:!1,noSSEHeader:!1,scMaxEachPostBytes:1e6,scMinPostsIntervalMs:30,scMaxBufferedPosts:30,xmux:{maxConcurrency:"16-32",maxConnections:0,cMaxReuseTimes:"64-128",cMaxLifetimeMs:0,hMaxRequestTimes:"800-900",hKeepAlivePeriod:0},downloadSettings:{address:"",port:443,network:"xhttp",security:"tls",tlsSettings:{},xhttpSettings:{path:"/yourpath"},sockopt:{}}}}}},P=()=>{switch(r){case"tcp":return["tcp","tcp-http"];case"grpc":return["grpc"];case"ws":return["ws"];case"httpupgrade":return["httpupgrade"];case"xhttp":return["xhttp"];default:return[]}},C=()=>{const j=f(u||"");if(j){A.error(j);return}try{if(!u){a(null),o(!1);return}a(JSON.parse(u)),o(!1)}catch{A.error(l("network_settings.errors.save_failed"))}},p=j=>{x(j),d(f(j))},g=j=>{const S=w[j];if(S){const R=JSON.stringify(S.content,null,2);x(R),d(null)}};return m.useEffect(()=>{c&&console.log(n)},[c,n]),m.useEffect(()=>{c&&n&&Object.keys(n).length>0&&x(JSON.stringify(n,null,2))},[c,n]),e.jsxs(ve,{open:c,onOpenChange:j=>{!j&&c&&C(),o(j)},children:[e.jsx(Ye,{asChild:!0,children:s??e.jsx(X,{variant:"link",children:l("network_settings.edit_protocol")})}),e.jsxs(pe,{className:"sm:max-w-[425px]",children:[e.jsx(we,{children:e.jsx(be,{children:l("network_settings.edit_protocol_config")})}),e.jsxs("div",{className:"space-y-4",children:[P().length>0&&e.jsx("div",{className:"flex flex-wrap gap-2 pt-2",children:P().map(j=>e.jsx(X,{variant:"outline",size:"sm",onClick:()=>g(j),children:l("network_settings.use_template",{template:w[j].label})},j))}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(bs,{className:`min-h-[200px] font-mono text-sm ${i?"border-red-500 focus-visible:ring-red-500":""}`,value:u,placeholder:P().length>0?l("network_settings.json_config_placeholder_with_template"):l("network_settings.json_config_placeholder"),onChange:j=>p(j.target.value)}),i&&e.jsx("p",{className:"text-sm text-red-500",children:i})]})]}),e.jsxs(Oe,{className:"gap-2",children:[e.jsx(X,{variant:"outline",onClick:()=>o(!1),children:l("common.cancel")}),e.jsx(X,{onClick:C,disabled:!!i,children:l("common.confirm")})]})]})]})}function up(s){throw new Error('Could not dynamically require "'+s+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}const ex={},sx=Object.freeze(Object.defineProperty({__proto__:null,default:ex},Symbol.toStringTag,{value:"Module"})),mp=Jo(sx),dn=s=>s.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""),tx=()=>{try{const s=zo.box.keyPair(),n=dn(Ja.encodeBase64(s.secretKey)),a=dn(Ja.encodeBase64(s.publicKey));return{privateKey:n,publicKey:a}}catch(s){throw console.error("Error generating x25519 key pair:",s),s}},ax=()=>{try{return tx()}catch(s){throw console.error("Error generating key pair:",s),s}},nx=s=>{const n=new Uint8Array(Math.ceil(s/2));return window.crypto.getRandomValues(n),Array.from(n).map(a=>a.toString(16).padStart(2,"0")).join("").substring(0,s)},rx=()=>{const s=Math.floor(Math.random()*8)*2+2;return nx(s)},lx=h.object({cipher:h.string().default("aes-128-gcm"),obfs:h.string().default("0"),obfs_settings:h.object({path:h.string().default(""),host:h.string().default("")}).default({})}),ix=h.object({tls:h.coerce.number().default(0),tls_settings:h.object({server_name:h.string().default(""),allow_insecure:h.boolean().default(!1)}).default({}),network:h.string().default("tcp"),network_settings:h.record(h.any()).default({})}),ox=h.object({server_name:h.string().default(""),allow_insecure:h.boolean().default(!1),network:h.string().default("tcp"),network_settings:h.record(h.any()).default({})}),cx=h.object({version:h.coerce.number().default(2),alpn:h.string().default("h2"),obfs:h.object({open:h.coerce.boolean().default(!1),type:h.string().default("salamander"),password:h.string().default("")}).default({}),tls:h.object({server_name:h.string().default(""),allow_insecure:h.boolean().default(!1)}).default({}),bandwidth:h.object({up:h.string().default(""),down:h.string().default("")}).default({})}),dx=h.object({tls:h.coerce.number().default(0),tls_settings:h.object({server_name:h.string().default(""),allow_insecure:h.boolean().default(!1)}).default({}),reality_settings:h.object({server_port:h.coerce.number().default(443),server_name:h.string().default(""),allow_insecure:h.boolean().default(!1),public_key:h.string().default(""),private_key:h.string().default(""),short_id:h.string().default("")}).default({}),network:h.string().default("tcp"),network_settings:h.record(h.any()).default({}),flow:h.string().default("")}),ux=h.object({version:h.coerce.number().default(5),congestion_control:h.string().default("bbr"),alpn:h.array(h.string()).default(["h3"]),udp_relay_mode:h.string().default("native"),tls:h.object({server_name:h.string().default(""),allow_insecure:h.boolean().default(!1)}).default({})}),$e={shadowsocks:{schema:lx,ciphers:["aes-128-gcm","aes-192-gcm","aes-256-gcm","chacha20-ietf-poly1305","2022-blake3-aes-128-gcm","2022-blake3-aes-256-gcm"]},vmess:{schema:ix,networkOptions:[{value:"tcp",label:"TCP"},{value:"ws",label:"Websocket"},{value:"grpc",label:"gRPC"}]},trojan:{schema:ox,networkOptions:[{value:"tcp",label:"TCP"},{value:"ws",label:"Websocket"},{value:"grpc",label:"gRPC"}]},hysteria:{schema:cx,versions:["1","2"],alpnOptions:["hysteria","http/1.1","h2","h3"]},vless:{schema:dx,networkOptions:[{value:"tcp",label:"TCP"},{value:"ws",label:"Websocket"},{value:"grpc",label:"gRPC"},{value:"kcp",label:"mKCP"},{value:"httpupgrade",label:"HttpUpgrade"},{value:"xhttp",label:"XHTTP"}],flowOptions:["none","xtls-rprx-direct","xtls-rprx-splice","xtls-rprx-vision"]},tuic:{schema:ux,versions:["5","4"],congestionControls:["bbr","cubic","new_reno"],alpnOptions:[{value:"h3",label:"HTTP/3"},{value:"h2",label:"HTTP/2"},{value:"http/1.1",label:"HTTP/1.1"}],udpRelayModes:[{value:"native",label:"Native"},{value:"quic",label:"QUIC"}]}},mx=({serverType:s,value:n,onChange:a})=>{const{t:r}=M("server"),l=s?$e[s]:null,c=l?.schema||h.record(h.any()),o=s?c.parse({}):{},u=fe({resolver:ye(c),defaultValues:o,mode:"onChange"});if(m.useEffect(()=>{if(!n||Object.keys(n).length===0){if(s){const p=c.parse({});u.reset(p)}}else u.reset(n)},[s,n,a,u,c]),m.useEffect(()=>{const p=u.watch(g=>{a(g)});return()=>p.unsubscribe()},[u,a]),!s||!l)return null;const C={shadowsocks:()=>e.jsxs(e.Fragment,{children:[e.jsx(b,{control:u.control,name:"cipher",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.shadowsocks.cipher.label")}),e.jsx(N,{children:e.jsxs(J,{onValueChange:p.onChange,value:p.value,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.shadowsocks.cipher.placeholder")})}),e.jsx(Y,{children:e.jsx(ns,{children:$e.shadowsocks.ciphers.map(g=>e.jsx(q,{value:g,children:g},g))})})]})})]})}),e.jsx(b,{control:u.control,name:"obfs",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.shadowsocks.obfs.label")}),e.jsx(N,{children:e.jsxs(J,{onValueChange:p.onChange,value:p.value,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.shadowsocks.obfs.placeholder")})}),e.jsx(Y,{children:e.jsxs(ns,{children:[e.jsx(q,{value:"0",children:r("dynamic_form.shadowsocks.obfs.none")}),e.jsx(q,{value:"http",children:r("dynamic_form.shadowsocks.obfs.http")})]})})]})})]})}),u.watch("obfs")==="http"&&e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{control:u.control,name:"obfs_settings.path",render:({field:p})=>e.jsxs(v,{className:"flex-1",children:[e.jsx(N,{children:e.jsx(T,{type:"text",placeholder:r("dynamic_form.shadowsocks.obfs_settings.path"),...p})}),e.jsx(k,{})]})}),e.jsx(b,{control:u.control,name:"obfs_settings.host",render:({field:p})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(N,{children:e.jsx(T,{type:"text",placeholder:r("dynamic_form.shadowsocks.obfs_settings.host"),...p})}),e.jsx(k,{})]})})]})]}),vmess:()=>e.jsxs(e.Fragment,{children:[e.jsx(b,{control:u.control,name:"tls",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.vmess.tls.label")}),e.jsx(N,{children:e.jsxs(J,{value:p.value?.toString(),onValueChange:g=>p.onChange(Number(g)),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.vmess.tls.placeholder")})}),e.jsxs(Y,{children:[e.jsx(q,{value:"0",children:r("dynamic_form.vmess.tls.disabled")}),e.jsx(q,{value:"1",children:r("dynamic_form.vmess.tls.enabled")})]})]})})]})}),u.watch("tls")==1&&e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{control:u.control,name:"tls_settings.server_name",render:({field:p})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(y,{children:r("dynamic_form.vmess.tls_settings.server_name.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:r("dynamic_form.vmess.tls_settings.server_name.placeholder"),...p})})]})}),e.jsx(b,{control:u.control,name:"tls_settings.allow_insecure",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.vmess.tls_settings.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(N,{children:e.jsx(Q,{checked:p.value,onCheckedChange:p.onChange})})})]})})]}),e.jsx(b,{control:u.control,name:"network",render:({field:p})=>e.jsxs(v,{children:[e.jsxs(y,{children:[r("dynamic_form.vmess.network.label"),e.jsx(ua,{value:u.watch("network_settings"),setValue:g=>u.setValue("network_settings",g),templateType:u.watch("network")})]}),e.jsx(N,{children:e.jsxs(J,{onValueChange:p.onChange,value:p.value,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.vmess.network.placeholder")})}),e.jsx(Y,{children:e.jsx(ns,{children:$e.vmess.networkOptions.map(g=>e.jsx(q,{value:g.value,className:"cursor-pointer",children:g.label},g.value))})})]})})]})})]}),trojan:()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{control:u.control,name:"server_name",render:({field:p})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(y,{children:r("dynamic_form.trojan.server_name.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:r("dynamic_form.trojan.server_name.placeholder"),...p,value:p.value||""})})]})}),e.jsx(b,{control:u.control,name:"allow_insecure",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.trojan.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(N,{children:e.jsx(Q,{checked:p.value||!1,onCheckedChange:p.onChange})})})]})})]}),e.jsx(b,{control:u.control,name:"network",render:({field:p})=>e.jsxs(v,{children:[e.jsxs(y,{children:[r("dynamic_form.trojan.network.label"),e.jsx(ua,{value:u.watch("network_settings")||{},setValue:g=>u.setValue("network_settings",g),templateType:u.watch("network")||"tcp"})]}),e.jsx(N,{children:e.jsxs(J,{onValueChange:p.onChange,value:p.value||"tcp",children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.trojan.network.placeholder")})}),e.jsx(Y,{children:e.jsx(ns,{children:$e.trojan.networkOptions.map(g=>e.jsx(q,{value:g.value,className:"cursor-pointer",children:g.label},g.value))})})]})})]})})]}),hysteria:()=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{control:u.control,name:"version",render:({field:p})=>e.jsxs(v,{className:"flex-1",children:[e.jsx(y,{children:r("dynamic_form.hysteria.version.label")}),e.jsx(N,{children:e.jsxs(J,{value:(p.value||2).toString(),onValueChange:g=>p.onChange(Number(g)),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.hysteria.version.placeholder")})}),e.jsx(Y,{children:e.jsx(ns,{children:$e.hysteria.versions.map(g=>e.jsxs(q,{value:g,className:"cursor-pointer",children:["V",g]},g))})})]})})]})}),u.watch("version")==1&&e.jsx(b,{control:u.control,name:"alpn",render:({field:p})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(y,{children:r("dynamic_form.hysteria.alpn.label")}),e.jsx(N,{children:e.jsxs(J,{value:p.value||"h2",onValueChange:p.onChange,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.hysteria.alpn.placeholder")})}),e.jsx(Y,{children:e.jsx(ns,{children:$e.hysteria.alpnOptions.map(g=>e.jsx(q,{value:g,children:g},g))})})]})})]})})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{control:u.control,name:"obfs.open",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.hysteria.obfs.label")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(N,{children:e.jsx(Q,{checked:p.value||!1,onCheckedChange:p.onChange})})})]})}),!!u.watch("obfs.open")&&e.jsxs(e.Fragment,{children:[u.watch("version")=="2"&&e.jsx(b,{control:u.control,name:"obfs.type",render:({field:p})=>e.jsxs(v,{className:"flex-1",children:[e.jsx(y,{children:r("dynamic_form.hysteria.obfs.type.label")}),e.jsx(N,{children:e.jsxs(J,{value:p.value||"salamander",onValueChange:p.onChange,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.hysteria.obfs.type.placeholder")})}),e.jsx(Y,{children:e.jsx(ns,{children:e.jsx(q,{value:"salamander",children:r("dynamic_form.hysteria.obfs.type.salamander")})})})]})})]})}),e.jsx(b,{control:u.control,name:"obfs.password",render:({field:p})=>e.jsxs(v,{className:u.watch("version")==2?"w-full":"flex-[2]",children:[e.jsx(y,{children:r("dynamic_form.hysteria.obfs.password.label")}),e.jsxs("div",{className:"relative",children:[e.jsx(N,{children:e.jsx(T,{placeholder:r("dynamic_form.hysteria.obfs.password.placeholder"),...p,value:p.value||"",className:"pr-9"})}),e.jsx(X,{type:"button",variant:"ghost",size:"icon",onClick:()=>{const g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",j=Array.from(crypto.getRandomValues(new Uint8Array(16))).map(S=>g[S%g.length]).join("");u.setValue("obfs.password",j),A.success(r("dynamic_form.hysteria.obfs.password.generate_success"))},className:"absolute right-0 top-0 h-full px-2  active:scale-90 transition-transform duration-150",children:e.jsx(Re,{icon:"ion:refresh-outline",className:"h-4 w-4 transition-transform hover:rotate-180 duration-300"})})]})]})})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{control:u.control,name:"tls.server_name",render:({field:p})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(y,{children:r("dynamic_form.hysteria.tls.server_name.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:r("dynamic_form.hysteria.tls.server_name.placeholder"),...p,value:p.value||""})})]})}),e.jsx(b,{control:u.control,name:"tls.allow_insecure",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.hysteria.tls.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(N,{children:e.jsx(Q,{checked:p.value||!1,onCheckedChange:p.onChange})})})]})})]}),e.jsx(b,{control:u.control,name:"bandwidth.up",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.hysteria.bandwidth.up.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:r("dynamic_form.hysteria.bandwidth.up.placeholder")+(u.watch("version")==2?r("dynamic_form.hysteria.bandwidth.up.bbr_tip"):""),className:"rounded-br-none rounded-tr-none",...p,value:p.value||""})}),e.jsx("div",{className:"pointer-events-none z-[-1] flex items-center rounded-md rounded-bl-none rounded-tl-none border border-l-0 border-input px-3 shadow-sm",children:e.jsx("span",{className:"text-gray-500",children:r("dynamic_form.hysteria.bandwidth.up.suffix")})})]})]})}),e.jsx(b,{control:u.control,name:"bandwidth.down",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.hysteria.bandwidth.down.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:r("dynamic_form.hysteria.bandwidth.down.placeholder")+(u.watch("version")==2?r("dynamic_form.hysteria.bandwidth.down.bbr_tip"):""),className:"rounded-br-none rounded-tr-none",...p,value:p.value||""})}),e.jsx("div",{className:"pointer-events-none z-[-1] flex items-center rounded-md rounded-bl-none rounded-tl-none border border-l-0 border-input px-3 shadow-sm",children:e.jsx("span",{className:"text-gray-500",children:r("dynamic_form.hysteria.bandwidth.down.suffix")})})]})]})})]}),vless:()=>e.jsxs(e.Fragment,{children:[e.jsx(b,{control:u.control,name:"tls",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.vless.tls.label")}),e.jsx(N,{children:e.jsxs(J,{value:p.value?.toString(),onValueChange:g=>p.onChange(Number(g)),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.vless.tls.placeholder")})}),e.jsxs(Y,{children:[e.jsx(q,{value:"0",children:r("dynamic_form.vless.tls.none")}),e.jsx(q,{value:"1",children:r("dynamic_form.vless.tls.tls")}),e.jsx(q,{value:"2",children:r("dynamic_form.vless.tls.reality")})]})]})})]})}),u.watch("tls")=="1"&&e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{control:u.control,name:"tls_settings.server_name",render:({field:p})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(y,{children:r("dynamic_form.vless.tls_settings.server_name.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:r("dynamic_form.vless.tls_settings.server_name.placeholder"),...p})})]})}),e.jsx(b,{control:u.control,name:"tls_settings.allow_insecure",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.vless.tls_settings.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(N,{children:e.jsx(Q,{checked:p.value,onCheckedChange:p.onChange})})})]})})]}),u.watch("tls")==2&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{control:u.control,name:"reality_settings.server_name",render:({field:p})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(y,{children:r("dynamic_form.vless.reality_settings.server_name.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:r("dynamic_form.vless.reality_settings.server_name.placeholder"),...p})})]})}),e.jsx(b,{control:u.control,name:"reality_settings.server_port",render:({field:p})=>e.jsxs(v,{className:"flex-1",children:[e.jsx(y,{children:r("dynamic_form.vless.reality_settings.server_port.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:r("dynamic_form.vless.reality_settings.server_port.placeholder"),...p})})]})}),e.jsx(b,{control:u.control,name:"reality_settings.allow_insecure",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.vless.reality_settings.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(N,{children:e.jsx(Q,{checked:p.value,onCheckedChange:p.onChange})})})]})})]}),e.jsx("div",{className:"flex items-end gap-2",children:e.jsx(b,{control:u.control,name:"reality_settings.private_key",render:({field:p})=>e.jsxs(v,{className:"flex-1",children:[e.jsx(y,{children:r("dynamic_form.vless.reality_settings.private_key.label")}),e.jsxs("div",{className:"relative",children:[e.jsx(N,{children:e.jsx(T,{...p,className:"pr-9"})}),e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(X,{type:"button",variant:"ghost",size:"icon",onClick:()=>{try{const g=ax();u.setValue("reality_settings.private_key",g.privateKey),u.setValue("reality_settings.public_key",g.publicKey),A.success(r("dynamic_form.vless.reality_settings.key_pair.success"))}catch{A.error(r("dynamic_form.vless.reality_settings.key_pair.error"))}},className:"absolute right-0 top-0 h-full px-2 active:scale-90 transition-transform duration-150",children:e.jsx(Re,{icon:"ion:key-outline",className:"h-4 w-4 transition-transform hover:rotate-180 duration-300"})})}),e.jsx(Mt,{children:e.jsx(ce,{children:e.jsx("p",{children:r("dynamic_form.vless.reality_settings.key_pair.generate")})})})]})]})]})})}),e.jsx(b,{control:u.control,name:"reality_settings.public_key",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.vless.reality_settings.public_key.label")}),e.jsx(N,{children:e.jsx(T,{...p})})]})}),e.jsx(b,{control:u.control,name:"reality_settings.short_id",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.vless.reality_settings.short_id.label")}),e.jsxs("div",{className:"relative",children:[e.jsx(N,{children:e.jsx(T,{...p,className:"pr-9",placeholder:r("dynamic_form.vless.reality_settings.short_id.placeholder")})}),e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(X,{type:"button",variant:"ghost",size:"icon",onClick:()=>{const g=rx();u.setValue("reality_settings.short_id",g),A.success(r("dynamic_form.vless.reality_settings.short_id.success"))},className:"absolute right-0 top-0 h-full px-2 active:scale-90 transition-transform duration-150",children:e.jsx(Re,{icon:"ion:refresh-outline",className:"h-4 w-4 transition-transform hover:rotate-180 duration-300"})})}),e.jsx(Mt,{children:e.jsx(ce,{children:e.jsx("p",{children:r("dynamic_form.vless.reality_settings.short_id.generate")})})})]})]}),e.jsx(z,{className:"text-xs text-muted-foreground",children:r("dynamic_form.vless.reality_settings.short_id.description")})]})})]}),e.jsx(b,{control:u.control,name:"network",render:({field:p})=>e.jsxs(v,{children:[e.jsxs(y,{children:[r("dynamic_form.vless.network.label"),e.jsx(ua,{value:u.watch("network_settings"),setValue:g=>u.setValue("network_settings",g),templateType:u.watch("network")})]}),e.jsx(N,{children:e.jsxs(J,{onValueChange:p.onChange,value:p.value,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.vless.network.placeholder")})}),e.jsx(Y,{children:e.jsx(ns,{children:$e.vless.networkOptions.map(g=>e.jsx(q,{value:g.value,className:"cursor-pointer",children:g.label},g.value))})})]})})]})}),e.jsx(b,{control:u.control,name:"flow",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.vless.flow.label")}),e.jsx(N,{children:e.jsxs(J,{onValueChange:g=>p.onChange(g==="none"?null:g),value:p.value||"none",children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.vless.flow.placeholder")})}),e.jsx(Y,{children:$e.vless.flowOptions.map(g=>e.jsx(q,{value:g,children:g},g))})]})})]})})]}),tuic:()=>e.jsxs(e.Fragment,{children:[e.jsx(b,{control:u.control,name:"version",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.tuic.version.label")}),e.jsx(N,{children:e.jsxs(J,{value:p.value?.toString(),onValueChange:g=>p.onChange(Number(g)),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.tuic.version.placeholder")})}),e.jsx(Y,{children:e.jsx(ns,{children:$e.tuic.versions.map(g=>e.jsxs(q,{value:g,children:["V",g]},g))})})]})})]})}),e.jsx(b,{control:u.control,name:"congestion_control",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.tuic.congestion_control.label")}),e.jsx(N,{children:e.jsxs(J,{onValueChange:p.onChange,value:p.value,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.tuic.congestion_control.placeholder")})}),e.jsx(Y,{children:e.jsx(ns,{children:$e.tuic.congestionControls.map(g=>e.jsx(q,{value:g,children:g.toUpperCase()},g))})})]})})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{control:u.control,name:"tls.server_name",render:({field:p})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(y,{children:r("dynamic_form.tuic.tls.server_name.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:r("dynamic_form.tuic.tls.server_name.placeholder"),...p})})]})}),e.jsx(b,{control:u.control,name:"tls.allow_insecure",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.tuic.tls.allow_insecure")}),e.jsx("div",{className:"py-2 text-center",children:e.jsx(N,{children:e.jsx(Q,{checked:p.value,onCheckedChange:p.onChange})})})]})})]}),e.jsx(b,{control:u.control,name:"alpn",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.tuic.tls.alpn.label")}),e.jsx(N,{children:e.jsx(st,{options:$e.tuic.alpnOptions,onChange:g=>p.onChange(g.map(j=>j.value)),value:$e.tuic.alpnOptions.filter(g=>p.value?.includes(g.value)),placeholder:r("dynamic_form.tuic.tls.alpn.placeholder"),emptyIndicator:e.jsx("p",{className:"text-center text-lg leading-10 text-gray-600 dark:text-gray-400",children:r("dynamic_form.tuic.tls.alpn.empty")})})})]})}),e.jsx(b,{control:u.control,name:"udp_relay_mode",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dynamic_form.tuic.udp_relay_mode.label")}),e.jsx(N,{children:e.jsxs(J,{onValueChange:p.onChange,value:p.value,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dynamic_form.tuic.udp_relay_mode.placeholder")})}),e.jsx(Y,{children:e.jsx(ns,{children:$e.tuic.udpRelayModes.map(g=>e.jsx(q,{value:g.value,children:g.label},g.value))})})]})})]})})]})};return e.jsx(je,{children:C[s]?.()})},xx=h.object({id:h.number().optional().nullable(),code:h.string().optional(),name:h.string().min(1,"form.name.error"),rate:h.string().min(1,"form.rate.error"),tags:h.array(h.string()).default([]),excludes:h.array(h.string()).default([]),ips:h.array(h.string()).default([]),group_ids:h.array(h.string()).default([]),host:h.string().min(1,"form.host.error"),port:h.string().min(1,"form.port.error"),server_port:h.string().min(1,"form.server_port.error"),parent_id:h.string().default("0").nullable(),route_ids:h.array(h.string()).default([]),protocol_settings:h.record(h.any()).default({}).nullable()}),St={id:null,code:"",name:"",rate:"1",tags:[],excludes:[],ips:[],group_ids:[],host:"",port:"",server_port:"",parent_id:"0",route_ids:[],protocol_settings:null};function hx(){const{t:s}=M("server"),{isOpen:n,setIsOpen:a,editingServer:r,setEditingServer:l,serverType:c,setServerType:o,refetch:u}=Zr(),[x,i]=m.useState([]),[d,f]=m.useState([]),[w,P]=m.useState([]),C=fe({resolver:ye(xx),defaultValues:St,mode:"onChange"});m.useEffect(()=>{p()},[n]),m.useEffect(()=>{r?.type&&r.type!==c&&o(r.type)},[r,c,o]),m.useEffect(()=>{r?r.type===c&&C.reset({...St,...r}):C.reset({...St,protocol_settings:$e[c].schema.parse({})})},[r,C,c]);const p=async()=>{if(!n)return;const[E,F,H]=await Promise.all([yt(),Sr(),Cr()]);i(E.data?.map(te=>({label:te.name,value:te.id.toString()}))||[]),f(F.data?.map(te=>({label:te.remarks,value:te.id.toString()}))||[]),P(H.data||[])},g=m.useMemo(()=>w?.filter(E=>(E.parent_id===0||E.parent_id===null)&&E.type===c&&E.id!==C.watch("id")),[c,w,C]),j=()=>e.jsxs(Cs,{children:[e.jsx(Ss,{asChild:!0,children:e.jsxs(D,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(Re,{icon:"ion:add"}),e.jsx("div",{children:s("form.add_node")})]})}),e.jsx(ps,{align:"start",children:e.jsx(Od,{children:Os.map(({type:E,label:F})=>e.jsx(ge,{onClick:()=>{o(E),a(!0)},className:"cursor-pointer",children:e.jsx(K,{variant:"outline",className:"text-white",style:{background:_s[E]},children:F})},E))})})]}),S=()=>{a(!1),l(null),C.reset(St)},R=async()=>{const E=C.getValues();(await kc({...E,type:c})).data&&(S(),A.success(s("form.success")),u())};return e.jsxs(ve,{open:n,onOpenChange:S,children:[j(),e.jsxs(pe,{className:"sm:max-w-[425px]",children:[e.jsxs(we,{children:[e.jsx(be,{children:s(r?"form.edit_node":"form.new_node")}),e.jsx(ke,{})]}),e.jsxs(Ne,{...C,children:[e.jsxs("div",{className:"grid gap-4",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(b,{control:C.control,name:"name",render:({field:E})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(y,{children:s("form.name.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("form.name.placeholder"),...E})}),e.jsx(k,{})]})}),e.jsx(b,{control:C.control,name:"rate",render:({field:E})=>e.jsxs(v,{className:"flex-[1]",children:[e.jsx(y,{children:s("form.rate.label")}),e.jsx("div",{className:"relative flex",children:e.jsx(N,{children:e.jsx(T,{type:"number",min:"0",step:"0.1",...E})})}),e.jsx(k,{})]})})]}),e.jsx(b,{control:C.control,name:"code",render:({field:E})=>e.jsxs(v,{children:[e.jsxs(y,{children:[s("form.code.label"),e.jsx("span",{className:"ml-1 text-xs text-muted-foreground",children:s("form.code.optional")})]}),e.jsx(N,{children:e.jsx(T,{placeholder:s("form.code.placeholder"),...E,value:E.value||""})}),e.jsx(k,{})]})}),e.jsx(b,{control:C.control,name:"tags",render:({field:E})=>e.jsxs(v,{children:[e.jsx(y,{children:s("form.tags.label")}),e.jsx(N,{children:e.jsx(La,{value:E.value,onChange:E.onChange,placeholder:s("form.tags.placeholder"),className:"w-full"})}),e.jsx(k,{})]})}),e.jsx(b,{control:C.control,name:"group_ids",render:({field:E})=>e.jsxs(v,{children:[e.jsxs(y,{className:"flex items-center justify-between",children:[s("form.groups.label"),e.jsx(aa,{dialogTrigger:e.jsx(D,{variant:"link",children:s("form.groups.add")}),refetch:p})]}),e.jsx(N,{children:e.jsx(st,{options:x,onChange:F=>E.onChange(F.map(H=>H.value)),value:x?.filter(F=>E.value.includes(F.value)),placeholder:s("form.groups.placeholder"),emptyIndicator:e.jsx("p",{className:"text-center text-lg leading-10 text-gray-600 dark:text-gray-400",children:s("form.groups.empty")})})}),e.jsx(k,{})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{control:C.control,name:"host",render:({field:E})=>e.jsxs(v,{children:[e.jsx(y,{children:s("form.host.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:s("form.host.placeholder"),...E})}),e.jsx(k,{})]})}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(b,{control:C.control,name:"port",render:({field:E})=>e.jsxs(v,{className:"flex-1",children:[e.jsxs(y,{className:"flex items-center gap-1.5",children:[s("form.port.label"),e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(Re,{icon:"ph:info-light",className:"size-3.5 cursor-help text-muted-foreground"})}),e.jsx(Mt,{children:e.jsx(ce,{side:"top",sideOffset:8,className:"max-w-80 p-3",children:e.jsx("p",{children:s("form.port.tooltip")})})})]})})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(N,{children:e.jsx(T,{placeholder:s("form.port.placeholder"),...E})}),e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(D,{type:"button",variant:"ghost",size:"icon",className:"size-6 shrink-0 text-muted-foreground/50 hover:text-muted-foreground",onClick:()=>{const F=E.value;F&&C.setValue("server_port",F)},children:e.jsx(Re,{icon:"tabler:arrows-right",className:"size-3"})})}),e.jsx(ce,{side:"right",children:e.jsx("p",{children:s("form.port.sync")})})]})})]}),e.jsx(k,{})]})}),e.jsx(b,{control:C.control,name:"server_port",render:({field:E})=>e.jsxs(v,{className:"flex-1",children:[e.jsxs(y,{className:"flex items-center gap-1.5",children:[s("form.server_port.label"),e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(Re,{icon:"ph:info-light",className:"size-3.5 cursor-help text-muted-foreground"})}),e.jsx(Mt,{children:e.jsx(ce,{side:"top",sideOffset:8,className:"max-w-80 p-3",children:e.jsx("p",{children:s("form.server_port.tooltip")})})})]})})]}),e.jsx(N,{children:e.jsx(T,{placeholder:s("form.server_port.placeholder"),...E})}),e.jsx(k,{})]})})]})]}),n&&e.jsx(mx,{serverType:c,value:C.watch("protocol_settings"),onChange:E=>C.setValue("protocol_settings",E,{shouldDirty:!0,shouldTouch:!0,shouldValidate:!0})}),e.jsx(b,{control:C.control,name:"parent_id",render:({field:E})=>e.jsxs(v,{children:[e.jsx(y,{children:s("form.parent.label")}),e.jsxs(J,{onValueChange:E.onChange,value:E.value?.toString()||"0",children:[e.jsx(N,{children:e.jsx(W,{children:e.jsx(Z,{placeholder:s("form.parent.placeholder")})})}),e.jsxs(Y,{children:[e.jsx(q,{value:"0",children:s("form.parent.none")}),g?.map(F=>e.jsx(q,{value:F.id.toString(),className:"cursor-pointer",children:F.name},F.id))]})]}),e.jsx(k,{})]})}),e.jsx(b,{control:C.control,name:"route_ids",render:({field:E})=>e.jsxs(v,{children:[e.jsx(y,{children:s("form.route.label")}),e.jsx(N,{children:e.jsx(st,{options:d,onChange:F=>E.onChange(F.map(H=>H.value)),value:d?.filter(F=>E.value.includes(F.value)),placeholder:s("form.route.placeholder"),emptyIndicator:e.jsx("p",{className:"text-center text-lg leading-10 text-gray-600 dark:text-gray-400",children:s("form.route.empty")})})}),e.jsx(k,{})]})})]}),e.jsxs(Oe,{className:"mt-6",children:[e.jsx(D,{type:"button",variant:"outline",onClick:S,children:s("form.cancel")}),e.jsx(D,{type:"submit",onClick:R,children:s("form.submit")})]})]})]})]})}function un({column:s,title:n,options:a}){const r=s?.getFacetedUniqueValues(),l=new Set(s?.getFilterValue());return e.jsxs(js,{children:[e.jsx(vs,{asChild:!0,children:e.jsxs(D,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(vt,{className:"mr-2 h-4 w-4"}),n,l?.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(Se,{orientation:"vertical",className:"mx-2 h-4"}),e.jsx(K,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:l.size}),e.jsx("div",{className:"hidden space-x-1 lg:flex",children:l.size>2?e.jsxs(K,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[l.size," selected"]}):a.filter(c=>l.has(c.value)).map(c=>e.jsx(K,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:c.label},c.value))})]})]})}),e.jsx(ms,{className:"w-[200px] p-0",align:"start",children:e.jsxs(Vs,{children:[e.jsx(Us,{placeholder:n}),e.jsxs(Ms,{children:[e.jsx(Ks,{children:"No results found."}),e.jsx(Ge,{children:a.map(c=>{const o=l.has(c.value);return e.jsxs(Ie,{onSelect:()=>{o?l.delete(c.value):l.add(c.value);const u=Array.from(l);s?.setFilterValue(u.length?u:void 0)},className:"cursor-pointer",children:[e.jsx("div",{className:_("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",o?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(As,{className:_("h-4 w-4")})}),c.icon&&e.jsx(c.icon,{className:`mr-2 h-4 w-4 text-muted-foreground text-${c.color}`}),e.jsx("span",{children:c.label}),r?.get(c.value)&&e.jsx("span",{className:"ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs",children:r.get(c.value)})]},c.value)})}),l.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(at,{}),e.jsx(Ge,{children:e.jsx(Ie,{onSelect:()=>s?.setFilterValue(void 0),className:"justify-center text-center cursor-pointer",children:"Clear filters"})})]})]})]})})]})}const px=[{value:Te.Shadowsocks,label:Os.find(s=>s.type===Te.Shadowsocks)?.label,color:_s[Te.Shadowsocks]},{value:Te.Vmess,label:Os.find(s=>s.type===Te.Vmess)?.label,color:_s[Te.Vmess]},{value:Te.Trojan,label:Os.find(s=>s.type===Te.Trojan)?.label,color:_s[Te.Trojan]},{value:Te.Hysteria,label:Os.find(s=>s.type===Te.Hysteria)?.label,color:_s[Te.Hysteria]},{value:Te.Vless,label:Os.find(s=>s.type===Te.Vless)?.label,color:_s[Te.Vless]}];function fx({table:s,saveOrder:n,isSortMode:a,groups:r}){const l=s.getState().columnFilters.length>0,{t:c}=M("server");return e.jsxs("div",{className:"flex items-center justify-between ",children:[e.jsxs("div",{className:"flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2",children:[!a&&e.jsxs(e.Fragment,{children:[e.jsx(hx,{}),e.jsx(T,{placeholder:c("toolbar.search"),value:s.getColumn("name")?.getFilterValue()??"",onChange:o=>s.getColumn("name")?.setFilterValue(o.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),e.jsxs("div",{className:"flex gap-x-2",children:[s.getColumn("type")&&e.jsx(un,{column:s.getColumn("type"),title:c("toolbar.type"),options:px}),s.getColumn("group_ids")&&e.jsx(un,{column:s.getColumn("group_ids"),title:c("columns.groups.title"),options:r.map(o=>({label:o.name,value:o.id.toString()}))})]}),l&&e.jsxs(D,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:[c("toolbar.reset"),e.jsx(Xe,{className:"ml-2 h-4 w-4"})]})]}),a&&e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx("p",{className:"text-sm text-muted-foreground",children:c("toolbar.sort.tip")})})]}),s.getRowCount()>0&&e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(D,{variant:a?"default":"outline",onClick:n,size:"sm",children:c(a?"toolbar.sort.save":"toolbar.sort.edit")})})]})}const gt=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M15.71 12.71a6 6 0 1 0-7.42 0a10 10 0 0 0-6.22 8.18a1 1 0 0 0 2 .22a8 8 0 0 1 15.9 0a1 1 0 0 0 1 .89h.11a1 1 0 0 0 .88-1.1a10 10 0 0 0-6.25-8.19M12 12a4 4 0 1 1 4-4a4 4 0 0 1-4 4"})}),kt={0:"bg-destructive/80 shadow-sm shadow-destructive/50",1:"bg-yellow-500/80 shadow-sm shadow-yellow-500/50",2:"bg-emerald-500/80 shadow-sm shadow-emerald-500/50"},gx=s=>{const{t:n}=M("server");return[{id:"drag-handle",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.sort")}),cell:()=>e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(Kt,{className:"size-4 cursor-move text-muted-foreground transition-colors hover:text-primary","aria-hidden":"true"})}),size:50},{accessorKey:"id",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.nodeId")}),cell:({row:a})=>{const r=a.getValue("id"),l=a.original.code;return e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsxs("div",{className:"group/id flex items-center space-x-2",children:[e.jsxs(K,{variant:"outline",className:_("border-2 font-medium transition-all duration-200 hover:opacity-80","flex items-center gap-1.5"),style:{borderColor:_s[a.original.type]},children:[e.jsx(ur,{className:"size-3"}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"flex items-center gap-0.5",children:l??r}),a.original.parent?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-sm text-muted-foreground/30",children:"→"}),e.jsx("span",{children:a.original.parent?.code||a.original.parent?.id})]}):""]})]}),e.jsx(D,{variant:"ghost",size:"icon",className:"size-5 text-muted-foreground/40 opacity-0 transition-all duration-200 hover:text-muted-foreground group-hover/id:opacity-100",onClick:c=>{c.stopPropagation(),zt(l||r.toString()).then(()=>{A.success(n("common:copy.success"))})},children:e.jsx(Za,{className:"size-3"})})]})}),e.jsxs(ce,{side:"top",className:"flex flex-col gap-1 p-3",children:[e.jsxs("p",{className:"font-medium",children:[Os.find(c=>c.type===a.original.type)?.label,a.original.parent_id?" (子节点)":""]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:l?"点击括号内容或复制按钮可复制节点代码":"点击复制按钮可复制节点ID"})]})]})})},size:50,enableSorting:!0},{accessorKey:"show",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.show")}),cell:({row:a})=>{const[r,l]=m.useState(!!a.getValue("show"));return e.jsx(Q,{checked:r,onCheckedChange:async c=>{l(c),Pc({id:a.original.id,type:a.original.type,show:c?1:0}).catch(()=>{l(!c),s()})},style:{backgroundColor:r?_s[a.original.type]:void 0}})},size:50,enableSorting:!1},{accessorKey:"name",header:({column:a})=>e.jsx("div",{className:"flex items-center",children:e.jsx(L,{column:a,title:n("columns.node"),tooltip:e.jsxs("div",{className:"grid grid-cols-1 gap-3 p-2",children:[e.jsxs("div",{className:"flex items-center space-x-2.5",children:[e.jsx("span",{className:_("h-2.5 w-2.5 rounded-full",kt[0])}),e.jsx("span",{className:"text-sm font-medium",children:n("columns.status.0")})]}),e.jsxs("div",{className:"flex items-center space-x-2.5",children:[e.jsx("span",{className:_("h-2.5 w-2.5 rounded-full",kt[1])}),e.jsx("span",{className:"text-sm font-medium",children:n("columns.status.1")})]}),e.jsxs("div",{className:"flex items-center space-x-2.5",children:[e.jsx("span",{className:_("h-2.5 w-2.5 rounded-full",kt[2])}),e.jsx("span",{className:"text-sm font-medium",children:n("columns.status.2")})]})]})})}),cell:({row:a})=>e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{children:e.jsxs("div",{className:"flex items-center space-x-2.5",children:[e.jsx("span",{className:_("size-2.5 flex-shrink-0 rounded-full transition-all duration-200",kt[a.original.available_status])}),e.jsx("span",{className:"text-left font-medium transition-colors hover:text-primary",children:a.getValue("name")})]})}),e.jsx(ce,{children:e.jsx("p",{className:"font-medium",children:n(`columns.status.${a.original.available_status}`)})})]})}),enableSorting:!1,size:200},{accessorKey:"host",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.address")}),cell:({row:a})=>{const r=`${a.original.host}:${a.original.port}`,l=a.original.port!==a.original.server_port;return e.jsxs("div",{className:"group relative flex min-w-0 items-start",children:[e.jsxs("div",{className:"flex min-w-0 flex-wrap items-baseline gap-x-1 gap-y-0.5 pr-7",children:[e.jsx("div",{className:"flex items-center ",children:e.jsxs("span",{className:"font-mono text-sm font-medium text-foreground/90",children:[a.original.host,":",a.original.port]})}),l&&e.jsxs("span",{className:"whitespace-nowrap text-[0.7rem] tracking-tight text-muted-foreground/40",children:["(",n("columns.internalPort")," ",a.original.server_port,")"]})]}),e.jsx("div",{className:"absolute right-0 top-0",children:e.jsx(je,{delayDuration:0,children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(D,{variant:"ghost",size:"icon",className:"size-6 text-muted-foreground/40 opacity-0 transition-all duration-200 hover:bg-muted/50 hover:text-muted-foreground group-hover:opacity-100",onClick:c=>{c.stopPropagation(),zt(r).then(()=>{A.success(n("common:copy.success"))})},children:e.jsx(Za,{className:"size-3"})})}),e.jsx(ce,{side:"top",sideOffset:10,children:n("columns.copyAddress")})]})})})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"online",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.onlineUsers.title"),tooltip:n("columns.onlineUsers.tooltip")}),cell:({row:a})=>e.jsxs("div",{className:"flex items-center space-x-2 px-4",children:[e.jsx(gt,{className:"size-4"}),e.jsx("span",{className:"font-medium",children:a.getValue("online")})]}),size:80,enableSorting:!0,enableHiding:!0},{accessorKey:"rate",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.rate.title"),tooltip:n("columns.rate.tooltip")}),cell:({row:a})=>e.jsxs(K,{variant:"secondary",className:"font-medium",children:[a.getValue("rate")," x"]}),size:80,enableSorting:!1,enableHiding:!0},{accessorKey:"group_ids",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.groups.title"),tooltip:n("columns.groups.tooltip")}),cell:({row:a})=>{const r=a.original.groups||[];return e.jsxs("div",{className:"flex flex-wrap gap-1.5",children:[r.map((l,c)=>e.jsx(K,{variant:"secondary",className:_("px-2 py-0.5 font-medium","bg-secondary/50 hover:bg-secondary/70","border border-border/50","transition-all duration-200","cursor-default select-none","flex items-center gap-1.5"),children:l.name},c)),r.length===0&&e.jsx("span",{className:"text-sm text-muted-foreground",children:n("columns.groups.empty")})]})},enableSorting:!1,filterFn:(a,r,l)=>{const c=a.getValue(r);return c?l.some(o=>c.includes(o)):!1}},{accessorKey:"type",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.type")}),cell:({row:a})=>{const r=a.getValue("type");return e.jsx(K,{variant:"outline",className:"border-2 font-medium transition-colors",style:{borderColor:_s[r]},children:r})},enableSorting:!1,enableHiding:!0,enableColumnFilter:!1,size:8e3},{id:"actions",header:({column:a})=>e.jsx(L,{className:"justify-end",column:a,title:n("columns.actions")}),cell:({row:a})=>{const{setIsOpen:r,setEditingServer:l,setServerType:c}=Zr();return e.jsx("div",{className:"flex justify-center",children:e.jsxs(Cs,{modal:!1,children:[e.jsx(Ss,{asChild:!0,children:e.jsx(D,{variant:"ghost",className:"h-8 w-8 p-0 hover:bg-muted","aria-label":n("columns.actions"),children:e.jsx(Ft,{className:"size-4"})})}),e.jsxs(ps,{align:"end",className:"w-40",children:[e.jsx(ge,{className:"cursor-pointer",onClick:()=>{c(a.original.type),l(a.original),r(!0)},children:e.jsxs("div",{className:"flex w-full items-center",children:[e.jsx(Lo,{className:"mr-2 size-4"}),n("columns.actions_dropdown.edit")]})}),e.jsxs(ge,{className:"cursor-pointer",onClick:async()=>{Dc({id:a.original.id}).then(({data:o})=>{o&&(A.success(n("columns.actions_dropdown.copy_success")),s())})},children:[e.jsx(Ao,{className:"mr-2 size-4"}),n("columns.actions_dropdown.copy")]}),e.jsx(et,{}),e.jsx(ge,{className:"cursor-pointer text-destructive focus:text-destructive",onSelect:o=>o.preventDefault(),children:e.jsx(We,{title:n("columns.actions_dropdown.delete.title"),description:n("columns.actions_dropdown.delete.description"),confirmText:n("columns.actions_dropdown.delete.confirm"),variant:"destructive",onConfirm:async()=>{Tc({id:a.original.id}).then(({data:o})=>{o&&(A.success(n("columns.actions_dropdown.delete_success")),s())})},children:e.jsxs("div",{className:"flex w-full items-center",children:[e.jsx(os,{className:"mr-2 size-4"}),n("columns.actions_dropdown.delete.confirm")]})})})]})]})})},size:50}]};function jx(){const[s,n]=m.useState({}),[a,r]=m.useState({"drag-handle":!1}),[l,c]=m.useState([]),[o,u]=m.useState({pageSize:500,pageIndex:0}),[x,i]=m.useState([]),[d,f]=m.useState(!1),[w,P]=m.useState({}),[C,p]=m.useState([]),{refetch:g}=ie({queryKey:["nodeList"],queryFn:async()=>{const{data:H}=await Cr();return p(H),H}}),{data:j}=ie({queryKey:["groups"],queryFn:async()=>{const{data:H}=await yt();return H}});m.useEffect(()=>{r({"drag-handle":d,show:!d,host:!d,online:!d,rate:!d,groups:!d,type:!1,actions:!d}),P({name:d?2e3:200}),u({pageSize:d?99999:500,pageIndex:0})},[d]);const S=(H,te)=>{d&&(H.dataTransfer.setData("text/plain",te.toString()),H.currentTarget.classList.add("opacity-50"))},R=(H,te)=>{if(!d)return;H.preventDefault(),H.currentTarget.classList.remove("bg-muted");const O=parseInt(H.dataTransfer.getData("text/plain"));if(O===te)return;const se=[...C],[He]=se.splice(O,1);se.splice(te,0,He),p(se)},E=async()=>{if(!d){f(!0);return}const H=C?.map((te,O)=>({id:te.id,order:O+1}));Ec(H).then(()=>{A.success("排序保存成功"),f(!1),g()}).finally(()=>{f(!1)})},F=ts({data:C||[],columns:gx(g),state:{sorting:x,columnVisibility:a,rowSelection:s,columnFilters:l,columnSizing:w,pagination:o},enableRowSelection:!0,onRowSelectionChange:n,onSortingChange:i,onColumnFiltersChange:c,onColumnVisibilityChange:r,onColumnSizingChange:P,onPaginationChange:u,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),getSortedRowModel:us(),getFacetedRowModel:Ts(),getFacetedUniqueValues:Ds(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(Xm,{refetch:g,children:e.jsx("div",{className:"space-y-4",children:e.jsx(xs,{table:F,toolbar:H=>e.jsx(fx,{table:H,refetch:g,saveOrder:E,isSortMode:d,groups:j||[]}),draggable:d,onDragStart:S,onDragEnd:H=>H.currentTarget.classList.remove("opacity-50"),onDragOver:H=>{H.preventDefault(),H.currentTarget.classList.add("bg-muted")},onDragLeave:H=>H.currentTarget.classList.remove("bg-muted"),onDrop:R,showPagination:!d})})})}function vx(){const{t:s}=M("server");return e.jsxs(Pe,{children:[e.jsxs(Ee,{children:[e.jsx(qe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("manage.title")}),e.jsx("p",{className:"text-muted-foreground mt-2",children:s("manage.description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(jx,{})})]})]})}const bx=Object.freeze(Object.defineProperty({__proto__:null,default:vx},Symbol.toStringTag,{value:"Module"}));function yx({table:s,refetch:n}){const a=s.getState().columnFilters.length>0,{t:r}=M("group");return e.jsx("div",{className:"flex items-center justify-between space-x-4",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsx(aa,{refetch:n}),e.jsx(T,{placeholder:r("toolbar.searchPlaceholder"),value:s.getColumn("name")?.getFilterValue()??"",onChange:l=>s.getColumn("name")?.setFilterValue(l.target.value),className:_("h-8  w-[150px] lg:w-[250px]",a&&"border-primary/50 ring-primary/20")}),a&&e.jsxs(D,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:[r("toolbar.reset"),e.jsx(Xe,{className:"ml-2 h-4 w-4"})]})]})})}const Nx=s=>{const{t:n}=M("group");return[{accessorKey:"id",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.id")}),cell:({row:a})=>e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(K,{variant:"outline",children:a.getValue("id")})}),enableSorting:!0},{accessorKey:"name",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.name")}),cell:({row:a})=>e.jsx("div",{className:"flex space-x-2",children:e.jsx("span",{className:"max-w-32 truncate font-medium",children:a.getValue("name")})})},{accessorKey:"users_count",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.usersCount")}),cell:({row:a})=>e.jsxs("div",{className:"flex items-center space-x-2 px-4",children:[e.jsx(gt,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:a.getValue("users_count")})]}),enableSorting:!0},{accessorKey:"server_count",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.serverCount")}),cell:({row:a})=>e.jsxs("div",{className:"flex items-center space-x-2 px-4",children:[e.jsx(ur,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:a.getValue("server_count")})]}),enableSorting:!0,size:8e3},{id:"actions",header:({column:a})=>e.jsx(L,{className:"justify-end",column:a,title:n("columns.actions")}),cell:({row:a})=>e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx(aa,{defaultValues:a.original,refetch:s,type:"edit",dialogTrigger:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:[e.jsx(qs,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:n("form.edit")})]})}),e.jsx(We,{title:n("messages.deleteConfirm"),description:n("messages.deleteDescription"),confirmText:n("messages.deleteButton"),variant:"destructive",onConfirm:async()=>{Ic({id:a.original.id}).then(({data:r})=>{r&&(A.success(n("messages.updateSuccess")),s())})},children:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(os,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:n("messages.deleteButton")})]})})]})}]};function _x(){const[s,n]=m.useState({}),[a,r]=m.useState({}),[l,c]=m.useState([]),[o,u]=m.useState([]),{data:x,refetch:i,isLoading:d}=ie({queryKey:["serverGroupList"],queryFn:async()=>{const{data:w}=await yt();return w}}),f=ts({data:x||[],columns:Nx(i),state:{sorting:o,columnVisibility:a,rowSelection:s,columnFilters:l},enableRowSelection:!0,onRowSelectionChange:n,onSortingChange:u,onColumnFiltersChange:c,onColumnVisibilityChange:r,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),getSortedRowModel:us(),getFacetedRowModel:Ts(),getFacetedUniqueValues:Ds(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(xs,{table:f,toolbar:w=>e.jsx(yx,{table:w,refetch:i}),isLoading:d})}function wx(){const{t:s}=M("group");return e.jsxs(Pe,{children:[e.jsxs(Ee,{children:[e.jsx(qe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(_x,{})})]})]})}const Cx=Object.freeze(Object.defineProperty({__proto__:null,default:wx},Symbol.toStringTag,{value:"Module"})),Sx=s=>h.object({remarks:h.string().min(1,s("form.validation.remarks")),match:h.array(h.string()),action:h.enum(["block","dns"]),action_value:h.string().optional()});function Xr({refetch:s,dialogTrigger:n,defaultValues:a={remarks:"",match:[],action:"block",action_value:""},type:r="add"}){const{t:l}=M("route"),c=fe({resolver:ye(Sx(l)),defaultValues:a,mode:"onChange"}),[o,u]=m.useState(!1);return e.jsxs(ve,{open:o,onOpenChange:u,children:[e.jsx(Ye,{asChild:!0,children:n||e.jsxs(D,{variant:"outline",size:"sm",className:"space-x-2",children:[e.jsx(Re,{icon:"ion:add"})," ",e.jsx("div",{children:l("form.add")})]})}),e.jsxs(pe,{className:"sm:max-w-[425px]",children:[e.jsxs(we,{children:[e.jsx(be,{children:l(r==="edit"?"form.edit":"form.create")}),e.jsx(ke,{})]}),e.jsxs(Ne,{...c,children:[e.jsx(b,{control:c.control,name:"remarks",render:({field:x})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(y,{children:l("form.remarks")}),e.jsx("div",{className:"relative",children:e.jsx(N,{children:e.jsx(T,{type:"text",placeholder:l("form.remarksPlaceholder"),...x})})}),e.jsx(k,{})]})}),e.jsx(b,{control:c.control,name:"match",render:({field:x})=>e.jsxs(v,{className:"flex-[2]",children:[e.jsx(y,{children:l("form.match")}),e.jsx("div",{className:"relative",children:e.jsx(N,{children:e.jsx(bs,{className:"min-h-[120px]",placeholder:l("form.matchPlaceholder"),value:x.value.join(`
`),onChange:i=>{x.onChange(i.target.value.split(`
`))}})})}),e.jsx(k,{})]})}),e.jsx(b,{control:c.control,name:"action",render:({field:x})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.action")}),e.jsx("div",{className:"relative",children:e.jsx(N,{children:e.jsxs(J,{onValueChange:x.onChange,defaultValue:x.value,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:l("form.actionPlaceholder")})}),e.jsxs(Y,{children:[e.jsx(q,{value:"block",children:l("actions.block")}),e.jsx(q,{value:"dns",children:l("actions.dns")})]})]})})}),e.jsx(k,{})]})}),c.watch("action")==="dns"&&e.jsx(b,{control:c.control,name:"action_value",render:({field:x})=>e.jsxs(v,{children:[e.jsx(y,{children:l("form.dns")}),e.jsx("div",{className:"relative",children:e.jsx(N,{children:e.jsx(T,{type:"text",placeholder:l("form.dnsPlaceholder"),...x})})})]})}),e.jsxs(Oe,{children:[e.jsx(Nt,{asChild:!0,children:e.jsx(D,{variant:"outline",children:l("form.cancel")})}),e.jsx(D,{type:"submit",onClick:()=>{Vc(c.getValues()).then(({data:x})=>{x&&(u(!1),s&&s(),toast.success(l(r==="edit"?"messages.updateSuccess":"messages.createSuccess")),c.reset())})},children:l("form.submit")})]})]})]})]})}function kx({table:s,refetch:n}){const a=s.getState().columnFilters.length>0,{t:r}=M("route");return e.jsx("div",{className:"flex items-center justify-between ",children:e.jsxs("div",{className:"flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2",children:[e.jsx(Xr,{refetch:n}),e.jsx(T,{placeholder:r("toolbar.searchPlaceholder"),value:s.getColumn("remarks")?.getFilterValue()??"",onChange:l=>s.getColumn("remarks")?.setFilterValue(l.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),a&&e.jsxs(D,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:[r("toolbar.reset"),e.jsx(Xe,{className:"ml-2 h-4 w-4"})]})]})})}function Tx({columns:s,data:n,refetch:a}){const[r,l]=m.useState({}),[c,o]=m.useState({}),[u,x]=m.useState([]),[i,d]=m.useState([]),f=ts({data:n,columns:s,state:{sorting:i,columnVisibility:c,rowSelection:r,columnFilters:u},enableRowSelection:!0,onRowSelectionChange:l,onSortingChange:d,onColumnFiltersChange:x,onColumnVisibilityChange:o,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),getSortedRowModel:us(),getFacetedRowModel:Ts(),getFacetedUniqueValues:Ds(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(xs,{table:f,toolbar:w=>e.jsx(kx,{table:w,refetch:a})})}const Dx=s=>{const{t:n}=M("route"),a={block:{icon:$o,variant:"destructive",className:"bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800"},dns:{icon:qo,variant:"secondary",className:"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-800"}};return[{accessorKey:"id",header:({column:r})=>e.jsx(L,{column:r,title:n("columns.id")}),cell:({row:r})=>e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(K,{variant:"outline",children:r.getValue("id")})}),enableSorting:!0,enableHiding:!1},{accessorKey:"remarks",header:({column:r})=>e.jsx(L,{column:r,title:n("columns.remarks")}),cell:({row:r})=>e.jsx("div",{className:"flex space-x-2",children:e.jsx("span",{className:"max-w-32 truncate font-medium sm:max-w-72 md:max-w-[31rem]",children:r.original.remarks})}),enableHiding:!1,enableSorting:!1},{accessorKey:"action_value",header:({column:r})=>e.jsx(L,{column:r,title:n("columns.action_value.title")}),cell:({row:r})=>{const l=r.original.action,c=r.original.action_value,o=r.original.match?.length||0;return e.jsxs("div",{className:"flex flex-col space-y-1",children:[e.jsx("span",{className:"text-sm font-medium",children:l==="dns"&&c?n("columns.action_value.dns",{value:c}):l==="block"?e.jsx("span",{className:"text-destructive",children:n("columns.action_value.block")}):n("columns.action_value.direct")}),e.jsx("span",{className:"text-xs text-muted-foreground",children:n("columns.matchRules",{count:o})})]})},enableHiding:!1,enableSorting:!1,size:300},{accessorKey:"action",header:({column:r})=>e.jsx(L,{column:r,title:n("columns.action")}),cell:({row:r})=>{const l=r.getValue("action"),c=a[l]?.icon;return e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs(K,{variant:a[l]?.variant||"default",className:_("flex items-center gap-1.5 px-3 py-1 capitalize",a[l]?.className),children:[c&&e.jsx(c,{className:"h-3.5 w-3.5"}),n(`actions.${l}`)]})})},enableSorting:!1,size:9e3},{id:"actions",header:()=>e.jsx("div",{className:"text-right",children:n("columns.actions")}),cell:({row:r})=>e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx(Xr,{defaultValues:r.original,refetch:s,type:"edit",dialogTrigger:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",children:[e.jsx(qs,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:n("form.edit")})]})}),e.jsx(We,{title:n("messages.deleteConfirm"),description:n("messages.deleteDescription"),confirmText:n("messages.deleteButton"),variant:"destructive",onConfirm:async()=>{Mc({id:r.original.id}).then(({data:l})=>{l&&(A.success(n("messages.deleteSuccess")),s())})},children:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(os,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:n("messages.deleteButton")})]})})]})}]};function Px(){const{t:s}=M("route"),[n,a]=m.useState([]);function r(){Sr().then(({data:l})=>{a(l)})}return m.useEffect(()=>{r()},[]),e.jsxs(Pe,{children:[e.jsxs(Ee,{children:[e.jsx(qe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(Tx,{data:n,columns:Dx(r),refetch:r})})]})]})}const Ex=Object.freeze(Object.defineProperty({__proto__:null,default:Px},Symbol.toStringTag,{value:"Module"})),el=m.createContext(void 0);function Rx({children:s,refreshData:n}){const[a,r]=m.useState(!1),[l,c]=m.useState(null);return e.jsx(el.Provider,{value:{isOpen:a,setIsOpen:r,editingPlan:l,setEditingPlan:c,refreshData:n},children:s})}function Aa(){const s=m.useContext(el);if(s===void 0)throw new Error("usePlanEdit must be used within a PlanEditProvider");return s}function Ix({table:s,saveOrder:n,isSortMode:a}){const{setIsOpen:r}=Aa(),{t:l}=M("subscribe");return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsxs(D,{variant:"outline",className:"space-x-2",size:"sm",onClick:()=>r(!0),children:[e.jsx(Re,{icon:"ion:add"}),e.jsx("div",{children:l("plan.add")})]}),e.jsx(T,{placeholder:l("plan.search"),value:s.getColumn("name")?.getFilterValue()??"",onChange:c=>s.getColumn("name")?.setFilterValue(c.target.value),className:"h-8 w-[150px] lg:w-[250px]"})]}),s.getRowCount()>0&&e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(D,{variant:a?"default":"outline",onClick:n,size:"sm",children:l(a?"plan.sort.save":"plan.sort.edit")})})]})}const mn={monthly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},quarterly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},half_yearly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},yearly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},two_yearly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},three_yearly:{color:"text-slate-700",bgColor:"bg-slate-100/80"},onetime:{color:"text-slate-700",bgColor:"bg-slate-100/80"},reset_traffic:{color:"text-slate-700",bgColor:"bg-slate-100/80"}},Vx=s=>{const{t:n}=M("subscribe");return[{id:"drag-handle",header:()=>null,cell:()=>e.jsx("div",{className:"cursor-move",children:e.jsx(Kt,{className:"size-4"})}),size:40,enableSorting:!1},{accessorKey:"id",header:({column:a})=>e.jsx(L,{column:a,title:n("plan.columns.id")}),cell:({row:a})=>e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(K,{variant:"outline",children:a.getValue("id")})}),enableSorting:!0,enableHiding:!1},{accessorKey:"show",header:({column:a})=>e.jsx(L,{column:a,title:n("plan.columns.show")}),cell:({row:a})=>e.jsx(Q,{defaultChecked:a.getValue("show"),onCheckedChange:r=>{na({id:a.original.id,show:r}).then(({data:l})=>{!l&&s()})}}),enableSorting:!1,enableHiding:!1},{accessorKey:"sell",header:({column:a})=>e.jsx(L,{column:a,title:n("plan.columns.sell")}),cell:({row:a})=>e.jsx(Q,{defaultChecked:a.getValue("sell"),onCheckedChange:r=>{na({id:a.original.id,sell:r}).then(({data:l})=>{!l&&s()})}}),enableSorting:!1,enableHiding:!1},{accessorKey:"renew",header:({column:a})=>e.jsx(L,{column:a,title:n("plan.columns.renew"),tooltip:n("plan.columns.renew_tooltip")}),cell:({row:a})=>e.jsx(Q,{defaultChecked:a.getValue("renew"),onCheckedChange:r=>{na({id:a.original.id,renew:r}).then(({data:l})=>{!l&&s()})}}),enableSorting:!1,enableHiding:!1},{accessorKey:"name",header:({column:a})=>e.jsx(L,{column:a,title:n("plan.columns.name")}),cell:({row:a})=>e.jsx("div",{className:"flex space-x-2",children:e.jsx("span",{className:"max-w-32 truncate font-medium sm:max-w-72 md:max-w-[31rem]",children:a.getValue("name")})}),enableSorting:!1,enableHiding:!1,size:900},{accessorKey:"users_count",header:({column:a})=>e.jsx(L,{column:a,title:n("plan.columns.stats")}),cell:({row:a})=>e.jsxs("div",{className:"flex items-center space-x-2 px-2",children:[e.jsx(gt,{}),e.jsx("span",{className:"max-w-32 truncate font-medium sm:max-w-72 md:max-w-[31rem]",children:a.getValue("users_count")})]}),enableSorting:!0},{accessorKey:"group",header:({column:a})=>e.jsx(L,{column:a,title:n("plan.columns.group")}),cell:({row:a})=>e.jsx("div",{className:"flex max-w-[600px] flex-wrap items-center gap-1.5 text-nowrap",children:e.jsx(K,{variant:"secondary",className:_("px-2 py-0.5 font-medium","bg-secondary/50 hover:bg-secondary/70","border border-border/50","transition-all duration-200","cursor-default select-none","flex items-center gap-1.5"),children:a.getValue("group")?.name})}),enableSorting:!1,enableHiding:!1},{accessorKey:"prices",header:({column:a})=>e.jsx(L,{column:a,title:n("plan.columns.price")}),cell:({row:a})=>{const r=a.getValue("prices"),l=[{period:n("plan.columns.price_period.monthly"),key:"monthly",unit:n("plan.columns.price_period.unit.month")},{period:n("plan.columns.price_period.quarterly"),key:"quarterly",unit:n("plan.columns.price_period.unit.quarter")},{period:n("plan.columns.price_period.half_yearly"),key:"half_yearly",unit:n("plan.columns.price_period.unit.half_year")},{period:n("plan.columns.price_period.yearly"),key:"yearly",unit:n("plan.columns.price_period.unit.year")},{period:n("plan.columns.price_period.two_yearly"),key:"two_yearly",unit:n("plan.columns.price_period.unit.two_year")},{period:n("plan.columns.price_period.three_yearly"),key:"three_yearly",unit:n("plan.columns.price_period.unit.three_year")},{period:n("plan.columns.price_period.onetime"),key:"onetime",unit:""},{period:n("plan.columns.price_period.reset_traffic"),key:"reset_traffic",unit:n("plan.columns.price_period.unit.times")}];return e.jsx("div",{className:"flex flex-wrap items-center gap-2",children:l.map(({period:c,key:o,unit:u})=>r[o]!=null&&e.jsxs(K,{variant:"secondary",className:_("px-2 py-0.5 font-medium transition-colors text-nowrap",mn[o].color,mn[o].bgColor,"border border-border/50","hover:bg-slate-200/80"),children:[c," ¥",r[o],u]},o))})},enableSorting:!1,size:9e3},{id:"actions",header:({column:a})=>e.jsx(L,{className:"justify-end",column:a,title:n("plan.columns.actions")}),cell:({row:a})=>{const{setIsOpen:r,setEditingPlan:l}=Aa();return e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",onClick:()=>{l(a.original),r(!0)},children:[e.jsx(qs,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:n("plan.columns.edit")})]}),e.jsx(We,{title:n("plan.columns.delete_confirm.title"),description:n("plan.columns.delete_confirm.description"),confirmText:n("plan.columns.delete"),variant:"destructive",onConfirm:async()=>{Xc({id:a.original.id}).then(({data:c})=>{c&&(A.success(n("plan.columns.delete_confirm.success")),s())})},children:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(os,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:n("plan.columns.delete")})]})})]})}}]},Mx=h.object({id:h.number().nullable(),group_id:h.union([h.number(),h.string()]).nullable().optional(),name:h.string().min(1).max(250),content:h.string().nullable().optional(),transfer_enable:h.union([h.number().min(0),h.string().min(1)]),prices:h.object({monthly:h.union([h.number(),h.string()]).nullable().optional(),quarterly:h.union([h.number(),h.string()]).nullable().optional(),half_yearly:h.union([h.number(),h.string()]).nullable().optional(),yearly:h.union([h.number(),h.string()]).nullable().optional(),two_yearly:h.union([h.number(),h.string()]).nullable().optional(),three_yearly:h.union([h.number(),h.string()]).nullable().optional(),onetime:h.union([h.number(),h.string()]).nullable().optional(),reset_traffic:h.union([h.number(),h.string()]).nullable().optional()}).default({}),speed_limit:h.union([h.number(),h.string()]).nullable().optional(),capacity_limit:h.union([h.number(),h.string()]).nullable().optional(),device_limit:h.union([h.number(),h.string()]).nullable().optional(),force_update:h.boolean().optional(),reset_traffic_method:h.number().nullable(),users_count:h.number().optional()}),sl=m.forwardRef(({className:s,...n},a)=>e.jsx(mr,{ref:a,className:_("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",s),...n,children:e.jsx(Ho,{className:_("flex items-center justify-center text-current"),children:e.jsx(As,{className:"h-4 w-4"})})}));sl.displayName=mr.displayName;const Tt={id:null,group_id:null,name:"",content:"",transfer_enable:"",prices:{monthly:"",quarterly:"",half_yearly:"",yearly:"",two_yearly:"",three_yearly:"",onetime:"",reset_traffic:""},speed_limit:"",capacity_limit:"",device_limit:"",force_update:!1,reset_traffic_method:null},Dt={monthly:{label:"月付",months:1,discount:1},quarterly:{label:"季付",months:3,discount:.95},half_yearly:{label:"半年付",months:6,discount:.9},yearly:{label:"年付",months:12,discount:.85},two_yearly:{label:"两年付",months:24,discount:.8},three_yearly:{label:"三年付",months:36,discount:.75},onetime:{label:"流量包",months:1,discount:1},reset_traffic:{label:"重置包",months:1,discount:1}},Fx=[{value:null,label:"follow_system"},{value:0,label:"monthly_first"},{value:1,label:"monthly_reset"},{value:2,label:"no_reset"},{value:3,label:"yearly_first"},{value:4,label:"yearly_reset"}];function Ox(){const{isOpen:s,setIsOpen:n,editingPlan:a,setEditingPlan:r,refreshData:l}=Aa(),[c,o]=m.useState(!1),{t:u}=M("subscribe"),x=fe({resolver:ye(Mx),defaultValues:{...Tt,...a||{}},mode:"onChange"});m.useEffect(()=>{a?x.reset({...Tt,...a}):x.reset(Tt)},[a,x]);const i=new Ea({html:!0}),[d,f]=m.useState();async function w(){yt().then(({data:p})=>{f(p)})}m.useEffect(()=>{s&&w()},[s]);const P=p=>{if(isNaN(p))return;const g=Object.entries(Dt).reduce((j,[S,R])=>{const E=p*R.months*R.discount;return{...j,[S]:E.toFixed(2)}},{});x.setValue("prices",g,{shouldDirty:!0})},C=()=>{n(!1),r(null),x.reset(Tt)};return e.jsx(ve,{open:s,onOpenChange:C,children:e.jsxs(pe,{children:[e.jsxs(we,{children:[e.jsx(be,{children:u(a?"plan.form.edit_title":"plan.form.add_title")}),e.jsx(ke,{})]}),e.jsxs(Ne,{...x,children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{control:x.control,name:"name",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:u("plan.form.name.label")}),e.jsx(N,{children:e.jsx(T,{placeholder:u("plan.form.name.placeholder"),...p})}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"group_id",render:({field:p})=>e.jsxs(v,{children:[e.jsxs(y,{className:"flex items-center justify-between",children:[u("plan.form.group.label"),e.jsx(aa,{dialogTrigger:e.jsx(D,{variant:"link",children:u("plan.form.group.add")}),refetch:w})]}),e.jsxs(J,{value:p.value?.toString()??"",onValueChange:g=>p.onChange(g?Number(g):null),children:[e.jsx(N,{children:e.jsx(W,{children:e.jsx(Z,{placeholder:u("plan.form.group.placeholder")})})}),e.jsx(Y,{children:d?.map(g=>e.jsx(q,{value:g.id.toString(),children:g.name},g.id))})]}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"transfer_enable",render:({field:p})=>e.jsxs(v,{className:"flex-1",children:[e.jsx(y,{children:u("plan.form.transfer.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(N,{children:e.jsx(T,{type:"number",min:0,placeholder:u("plan.form.transfer.placeholder"),className:"rounded-r-none",...p})}),e.jsx("div",{className:"flex items-center rounded-r-md border border-l-0 border-input bg-muted px-3 text-sm text-muted-foreground",children:u("plan.form.transfer.unit")})]}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"speed_limit",render:({field:p})=>e.jsxs(v,{className:"flex-1",children:[e.jsx(y,{children:u("plan.form.speed.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(N,{children:e.jsx(T,{type:"number",min:0,placeholder:u("plan.form.speed.placeholder"),className:"rounded-r-none",...p,value:p.value??""})}),e.jsx("div",{className:"flex items-center rounded-r-md border border-l-0 border-input bg-muted px-3 text-sm text-muted-foreground",children:u("plan.form.speed.unit")})]}),e.jsx(k,{})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center",children:[e.jsx("div",{className:"flex-grow border-t border-gray-200 dark:border-gray-700"}),e.jsx("h3",{className:"mx-4 text-sm font-medium text-gray-500 dark:text-gray-400",children:u("plan.form.price.title")}),e.jsx("div",{className:"flex-grow border-t border-gray-200 dark:border-gray-700"})]}),e.jsxs("div",{className:"ml-4 flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2",children:e.jsx("span",{className:"text-sm font-medium text-gray-400",children:"¥"})}),e.jsx(T,{type:"number",placeholder:u("plan.form.price.base_price"),className:"h-7 w-32 border-0 bg-gray-50 pl-6 pr-2 text-sm shadow-none ring-1 ring-gray-200 transition-shadow focus-visible:ring-2 focus-visible:ring-primary dark:bg-gray-800/50 dark:ring-gray-700 dark:placeholder:text-gray-500",onChange:p=>{const g=parseFloat(p.target.value);P(g)}})]}),e.jsx(je,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(D,{variant:"outline",size:"sm",className:"h-7 text-xs",onClick:()=>{const p=Object.keys(Dt).reduce((g,j)=>({...g,[j]:""}),{});x.setValue("prices",p,{shouldDirty:!0})},children:u("plan.form.price.clear.button")})}),e.jsx(ce,{side:"top",align:"end",children:e.jsx("p",{className:"text-xs",children:u("plan.form.price.clear.tooltip")})})]})})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-3 lg:grid-cols-3",children:Object.entries(Dt).filter(([p])=>!["onetime","reset_traffic"].includes(p)).map(([p,g])=>e.jsx("div",{className:"group relative rounded-md bg-card p-2 ring-1 ring-gray-200 transition-all hover:ring-primary dark:ring-gray-800",children:e.jsx(b,{control:x.control,name:`prices.${p}`,render:({field:j})=>e.jsxs(v,{children:[e.jsxs(y,{className:"text-xs font-medium text-muted-foreground",children:[u(`plan.columns.price_period.${p}`),e.jsxs("span",{className:"ml-1 text-[10px] text-gray-400",children:["(",g.months===1?u("plan.form.price.period.monthly"):u("plan.form.price.period.months",{count:g.months}),")"]})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2",children:e.jsx("span",{className:"text-sm font-medium text-gray-400",children:"¥"})}),e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:"0.00",min:0,...j,value:j.value??"",onChange:S=>j.onChange(S.target.value),className:"h-7 border-0 bg-gray-50 pl-6 pr-2 text-sm shadow-none ring-1 ring-gray-200 transition-shadow focus-visible:ring-2 focus-visible:ring-primary dark:bg-gray-800/50 dark:ring-gray-700 dark:placeholder:text-gray-500"})})]})]})})},p))}),e.jsx("div",{className:"grid grid-cols-1 gap-3 md:grid-cols-2",children:Object.entries(Dt).filter(([p])=>["onetime","reset_traffic"].includes(p)).map(([p,g])=>e.jsx("div",{className:"rounded-md border border-dashed border-gray-200 bg-muted/30 p-3 dark:border-gray-700",children:e.jsx(b,{control:x.control,name:`prices.${p}`,render:({field:j})=>e.jsx(v,{children:e.jsxs("div",{className:"flex flex-col gap-2 md:flex-row md:items-center md:justify-between",children:[e.jsxs("div",{className:"space-y-0",children:[e.jsx(y,{className:"text-xs font-medium",children:u(`plan.columns.price_period.${p}`)}),e.jsx("p",{className:"text-[10px] text-muted-foreground",children:u(p==="onetime"?"plan.form.price.onetime_desc":"plan.form.price.reset_desc")})]}),e.jsxs("div",{className:"relative w-full md:w-32",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2",children:e.jsx("span",{className:"text-sm font-medium text-gray-400",children:"¥"})}),e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:"0.00",min:0,...j,className:"h-7 border-0 bg-gray-50 pl-6 pr-2 text-sm shadow-none ring-1 ring-gray-200 transition-shadow focus-visible:ring-2 focus-visible:ring-primary dark:bg-gray-800/50 dark:ring-gray-700 dark:placeholder:text-gray-500"})})]})]})})})},p))})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(b,{control:x.control,name:"device_limit",render:({field:p})=>e.jsxs(v,{className:"flex-1",children:[e.jsx(y,{children:u("plan.form.device.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(N,{children:e.jsx(T,{type:"number",min:0,placeholder:u("plan.form.device.placeholder"),className:"rounded-r-none",...p,value:p.value??""})}),e.jsx("div",{className:"flex items-center rounded-r-md border border-l-0 border-input bg-muted px-3 text-sm text-muted-foreground",children:u("plan.form.device.unit")})]}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"capacity_limit",render:({field:p})=>e.jsxs(v,{className:"flex-1",children:[e.jsx(y,{children:u("plan.form.capacity.label")}),e.jsxs("div",{className:"relative flex",children:[e.jsx(N,{children:e.jsx(T,{type:"number",min:0,placeholder:u("plan.form.capacity.placeholder"),className:"rounded-r-none",...p,value:p.value??""})}),e.jsx("div",{className:"flex items-center rounded-r-md border border-l-0 border-input bg-muted px-3 text-sm text-muted-foreground",children:u("plan.form.capacity.unit")})]}),e.jsx(k,{})]})})]}),e.jsx(b,{control:x.control,name:"reset_traffic_method",render:({field:p})=>e.jsxs(v,{children:[e.jsx(y,{children:u("plan.form.reset_method.label")}),e.jsxs(J,{value:p.value?.toString()??"null",onValueChange:g=>p.onChange(g=="null"?null:Number(g)),children:[e.jsx(N,{children:e.jsx(W,{children:e.jsx(Z,{placeholder:u("plan.form.reset_method.placeholder")})})}),e.jsx(Y,{children:Fx.map(g=>e.jsx(q,{value:g.value?.toString()??"null",children:u(`plan.form.reset_method.options.${g.label}`)},g.value))})]}),e.jsx(z,{className:"text-xs",children:u("plan.form.reset_method.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:x.control,name:"content",render:({field:p})=>{const[g,j]=m.useState(!1);return e.jsxs(v,{className:"space-y-2",children:[e.jsxs(y,{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[u("plan.form.content.label"),e.jsx(je,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(D,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:()=>j(!g),children:g?e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4",children:[e.jsx("path",{d:"M10 12.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5z"}),e.jsx("path",{fillRule:"evenodd",d:"M.664 10.59a1.651 1.651 0 010-1.186A10.004 10.004 0 0110 3c4.257 0 7.893 2.66 9.336 6.41.147.381.146.804 0 1.186A10.004 10.004 0 0110 17c-4.257 0-7.893-2.66-9.336-6.41zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]}):e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4",children:[e.jsx("path",{fillRule:"evenodd",d:"M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06l-1.745-1.745a10.029 10.029 0 003.3-4.38 1.651 1.651 0 000-1.185A10.004 10.004 0 009.999 3a9.956 9.956 0 00-4.744 1.194L3.28 2.22zM7.752 6.69l1.092 1.092a2.5 2.5 0 013.374 3.373l1.091 1.092a4 4 0 00-5.557-5.557z",clipRule:"evenodd"}),e.jsx("path",{d:"M10.748 13.93l2.523 2.523a9.987 9.987 0 01-3.27.547c-4.258 0-7.894-2.66-9.337-6.41a1.651 1.651 0 010-1.186A10.007 10.007 0 012.839 6.02L6.07 9.252a4 4 0 004.678 4.678z"})]})})}),e.jsx(ce,{side:"top",children:e.jsx("p",{className:"text-xs",children:u(g?"plan.form.content.preview_button.hide":"plan.form.content.preview_button.show")})})]})})]}),e.jsx(je,{children:e.jsxs(xe,{children:[e.jsx(he,{asChild:!0,children:e.jsx(D,{variant:"outline",size:"sm",onClick:()=>{p.onChange(u("plan.form.content.template.content"))},children:u("plan.form.content.template.button")})}),e.jsx(ce,{side:"left",align:"center",children:e.jsx("p",{className:"text-xs",children:u("plan.form.content.template.tooltip")})})]})})]}),e.jsxs("div",{className:`grid gap-4 ${g?"grid-cols-1 lg:grid-cols-2":"grid-cols-1"}`,children:[e.jsx("div",{className:"space-y-2",children:e.jsx(N,{children:e.jsx(Ra,{style:{height:"400px"},value:p.value||"",renderHTML:S=>i.render(S),onChange:({text:S})=>p.onChange(S),config:{view:{menu:!0,md:!0,html:!1},canView:{menu:!0,md:!0,html:!1,fullScreen:!1,hideMenu:!1}},placeholder:u("plan.form.content.placeholder"),className:"rounded-md border"})})}),g&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:u("plan.form.content.preview")}),e.jsx("div",{className:"prose prose-sm dark:prose-invert h-[400px] max-w-none overflow-y-auto rounded-md border p-4",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:i.render(p.value||"")}})})]})]}),e.jsx(z,{className:"text-xs",children:u("plan.form.content.description")}),e.jsx(k,{})]})}})]}),e.jsx(Oe,{className:"mt-6",children:e.jsxs("div",{className:"flex w-full items-center justify-between",children:[e.jsx("div",{className:"flex-shrink-0",children:a&&e.jsx(b,{control:x.control,name:"force_update",render:({field:p})=>e.jsxs(v,{className:"flex flex-row items-center space-x-2 space-y-0",children:[e.jsx(N,{children:e.jsx(sl,{checked:p.value,onCheckedChange:p.onChange})}),e.jsx("div",{className:"",children:e.jsx(y,{className:"text-sm",children:u("plan.form.force_update.label")})})]})})}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(D,{type:"button",variant:"outline",onClick:C,children:u("plan.form.submit.cancel")}),e.jsx(D,{type:"submit",disabled:c,onClick:()=>{x.handleSubmit(async p=>{o(!0),(await Zc(p)).data&&(A.success(u(a?"plan.form.submit.success.update":"plan.form.submit.success.add")),C(),l()),o(!1)})()},children:u(c?"plan.form.submit.submitting":"plan.form.submit.submit")})]})]})})]})]})})}function zx(){const[s,n]=m.useState({}),[a,r]=m.useState({"drag-handle":!1}),[l,c]=m.useState([]),[o,u]=m.useState([]),[x,i]=m.useState(!1),[d,f]=m.useState({pageSize:20,pageIndex:0}),[w,P]=m.useState([]),{refetch:C}=ie({queryKey:["planList"],queryFn:async()=>{const{data:R}=await Hs();return P(R),R}});m.useEffect(()=>{r({"drag-handle":x}),f({pageSize:x?99999:10,pageIndex:0})},[x]);const p=(R,E)=>{x&&(R.dataTransfer.setData("text/plain",E.toString()),R.currentTarget.classList.add("opacity-50"))},g=(R,E)=>{if(!x)return;R.preventDefault(),R.currentTarget.classList.remove("bg-muted");const F=parseInt(R.dataTransfer.getData("text/plain"));if(F===E)return;const H=[...w],[te]=H.splice(F,1);H.splice(E,0,te),P(H)},j=async()=>{if(!x){i(!0);return}const R=w?.map(E=>E.id);ed(R).then(()=>{A.success("排序保存成功"),i(!1),C()}).finally(()=>{i(!1)})},S=ts({data:w||[],columns:Vx(C),state:{sorting:o,columnVisibility:a,rowSelection:s,columnFilters:l,pagination:d},enableRowSelection:!0,onPaginationChange:f,onRowSelectionChange:n,onSortingChange:u,onColumnFiltersChange:c,onColumnVisibilityChange:r,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),getSortedRowModel:us(),getFacetedRowModel:Ts(),getFacetedUniqueValues:Ds(),initialState:{columnPinning:{right:["actions"]}},pageCount:x?1:void 0});return e.jsx(Rx,{refreshData:C,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(xs,{table:S,toolbar:R=>e.jsx(Ix,{table:R,refetch:C,saveOrder:j,isSortMode:x}),draggable:x,onDragStart:p,onDragEnd:R=>R.currentTarget.classList.remove("opacity-50"),onDragOver:R=>{R.preventDefault(),R.currentTarget.classList.add("bg-muted")},onDragLeave:R=>R.currentTarget.classList.remove("bg-muted"),onDrop:g,showPagination:!x}),e.jsx(Ox,{})]})})}function Lx(){const{t:s}=M("subscribe");return e.jsxs(Pe,{children:[e.jsxs(Ee,{children:[e.jsx(qe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("plan.title")}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:s("plan.page.description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(zx,{})})]})]})}const Ax=Object.freeze(Object.defineProperty({__proto__:null,default:Lx},Symbol.toStringTag,{value:"Module"})),Ys=[{value:re.PENDING,label:ot[re.PENDING],icon:Uo,color:ct[re.PENDING]},{value:re.PROCESSING,label:ot[re.PROCESSING],icon:xr,color:ct[re.PROCESSING]},{value:re.COMPLETED,label:ot[re.COMPLETED],icon:va,color:ct[re.COMPLETED]},{value:re.CANCELLED,label:ot[re.CANCELLED],icon:hr,color:ct[re.CANCELLED]},{value:re.DISCOUNTED,label:ot[re.DISCOUNTED],icon:va,color:ct[re.DISCOUNTED]}],ut=[{value:me.PENDING,label:wt[me.PENDING],icon:Ko,color:Ct[me.PENDING]},{value:me.PROCESSING,label:wt[me.PROCESSING],icon:xr,color:Ct[me.PROCESSING]},{value:me.VALID,label:wt[me.VALID],icon:va,color:Ct[me.VALID]},{value:me.INVALID,label:wt[me.INVALID],icon:hr,color:Ct[me.INVALID]}];function Pt({column:s,title:n,options:a}){const r=s?.getFacetedUniqueValues(),l=s?.getFilterValue(),c=Array.isArray(l)?new Set(l):l!==void 0?new Set([l]):new Set;return e.jsxs(js,{children:[e.jsx(vs,{asChild:!0,children:e.jsxs(D,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(vt,{className:"mr-2 h-4 w-4"}),n,c?.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(Se,{orientation:"vertical",className:"mx-2 h-4"}),e.jsx(K,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:c.size}),e.jsx("div",{className:"hidden space-x-1 lg:flex",children:c.size>2?e.jsxs(K,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[c.size," selected"]}):a.filter(o=>c.has(o.value)).map(o=>e.jsx(K,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:o.label},o.value))})]})]})}),e.jsx(ms,{className:"w-[200px] p-0",align:"start",children:e.jsxs(Vs,{children:[e.jsx(Us,{placeholder:n}),e.jsxs(Ms,{children:[e.jsx(Ks,{children:"No results found."}),e.jsx(Ge,{children:a.map(o=>{const u=c.has(o.value);return e.jsxs(Ie,{onSelect:()=>{const x=new Set(c);u?x.delete(o.value):x.add(o.value);const i=Array.from(x);s?.setFilterValue(i.length?i:void 0)},children:[e.jsx("div",{className:_("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",u?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(As,{className:_("h-4 w-4")})}),o.icon&&e.jsx(o.icon,{className:`mr-2 h-4 w-4 text-muted-foreground text-${o.color}`}),e.jsx("span",{children:o.label}),r?.get(o.value)&&e.jsx("span",{className:"ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs",children:r.get(o.value)})]},o.value)})}),c.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(at,{}),e.jsx(Ge,{children:e.jsx(Ie,{onSelect:()=>s?.setFilterValue(void 0),className:"justify-center text-center",children:"Clear filters"})})]})]})]})})]})}const $x=h.object({email:h.string().min(1),plan_id:h.number(),period:h.string(),total_amount:h.number()}),qx={email:"",plan_id:0,total_amount:0,period:""};function tl({refetch:s,trigger:n,defaultValues:a}){const{t:r}=M("order"),[l,c]=m.useState(!1),o=fe({resolver:ye($x),defaultValues:{...qx,...a},mode:"onChange"}),[u,x]=m.useState([]);return m.useEffect(()=>{l&&Hs().then(({data:i})=>{x(i)})},[l]),e.jsxs(ve,{open:l,onOpenChange:c,children:[e.jsx(Ye,{asChild:!0,children:n||e.jsxs(D,{variant:"outline",size:"sm",className:"h-8 space-x-2",children:[e.jsx(Re,{icon:"ion:add"}),e.jsx("div",{children:r("dialog.addOrder")})]})}),e.jsxs(pe,{className:"sm:max-w-[425px]",children:[e.jsxs(we,{children:[e.jsx(be,{children:r("dialog.assignOrder")}),e.jsx(ke,{})]}),e.jsxs(Ne,{...o,children:[e.jsx(b,{control:o.control,name:"email",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dialog.fields.userEmail")}),e.jsx(N,{children:e.jsx(T,{placeholder:r("dialog.placeholders.email"),...i})})]})}),e.jsx(b,{control:o.control,name:"plan_id",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dialog.fields.subscriptionPlan")}),e.jsx(N,{children:e.jsxs(J,{value:i.value?i.value?.toString():void 0,onValueChange:d=>i.onChange(parseInt(d)),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dialog.placeholders.plan")})}),e.jsx(Y,{children:u.map(d=>e.jsx(q,{value:d.id.toString(),children:d.name},d.id))})]})})]})}),e.jsx(b,{control:o.control,name:"period",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dialog.fields.orderPeriod")}),e.jsx(N,{children:e.jsxs(J,{value:i.value,onValueChange:i.onChange,children:[e.jsx(W,{children:e.jsx(Z,{placeholder:r("dialog.placeholders.period")})}),e.jsx(Y,{children:Object.keys(fu).map(d=>e.jsx(q,{value:d,children:r(`period.${d}`)},d))})]})})]})}),e.jsx(b,{control:o.control,name:"total_amount",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:r("dialog.fields.paymentAmount")}),e.jsx(N,{children:e.jsx(T,{type:"number",placeholder:r("dialog.placeholders.amount"),value:i.value/100,onChange:d=>i.onChange(parseFloat(d.currentTarget.value)*100)})}),e.jsx(k,{})]})}),e.jsxs(Oe,{children:[e.jsx(D,{variant:"outline",onClick:()=>c(!1),children:r("dialog.actions.cancel")}),e.jsx(D,{type:"submit",onClick:()=>{o.handleSubmit(i=>{rd(i).then(({data:d})=>{d&&(s&&s(),o.reset(),c(!1),A.success(r("dialog.messages.addSuccess")))})})()},children:r("dialog.actions.confirm")})]})]})]})]})}function Hx({table:s,refetch:n}){const{t:a}=M("order"),r=s.getState().columnFilters.length>0,l=Object.values(is).filter(x=>typeof x=="number").map(x=>({label:a(`type.${is[x]}`),value:x,color:x===is.NEW?"green-500":x===is.RENEWAL?"blue-500":x===is.UPGRADE?"purple-500":"orange-500"})),c=Object.values(De).map(x=>({label:a(`period.${x}`),value:x,color:x===De.MONTH_PRICE?"slate-500":x===De.QUARTER_PRICE?"cyan-500":x===De.HALF_YEAR_PRICE?"indigo-500":x===De.YEAR_PRICE?"violet-500":x===De.TWO_YEAR_PRICE?"fuchsia-500":x===De.THREE_YEAR_PRICE?"pink-500":x===De.ONETIME_PRICE?"rose-500":"orange-500"})),o=Object.values(re).filter(x=>typeof x=="number").map(x=>({label:a(`status.${re[x]}`),value:x,icon:x===re.PENDING?Ys[0].icon:x===re.PROCESSING?Ys[1].icon:x===re.COMPLETED?Ys[2].icon:x===re.CANCELLED?Ys[3].icon:Ys[4].icon,color:x===re.PENDING?"yellow-500":x===re.PROCESSING?"blue-500":x===re.COMPLETED?"green-500":x===re.CANCELLED?"red-500":"green-500"})),u=Object.values(me).filter(x=>typeof x=="number").map(x=>({label:a(`commission.${me[x]}`),value:x,icon:x===me.PENDING?ut[0].icon:x===me.PROCESSING?ut[1].icon:x===me.VALID?ut[2].icon:ut[3].icon,color:x===me.PENDING?"yellow-500":x===me.PROCESSING?"blue-500":x===me.VALID?"green-500":"red-500"}));return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(tl,{refetch:n}),e.jsx(T,{placeholder:a("search.placeholder"),value:s.getColumn("trade_no")?.getFilterValue()??"",onChange:x=>s.getColumn("trade_no")?.setFilterValue(x.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),e.jsxs("div",{className:"flex flex-wrap gap-x-2",children:[s.getColumn("type")&&e.jsx(Pt,{column:s.getColumn("type"),title:a("table.columns.type"),options:l}),s.getColumn("period")&&e.jsx(Pt,{column:s.getColumn("period"),title:a("table.columns.period"),options:c}),s.getColumn("status")&&e.jsx(Pt,{column:s.getColumn("status"),title:a("table.columns.status"),options:o}),s.getColumn("commission_status")&&e.jsx(Pt,{column:s.getColumn("commission_status"),title:a("table.columns.commissionStatus"),options:u})]}),r&&e.jsxs(D,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:[a("actions.reset"),e.jsx(Xe,{className:"ml-2 h-4 w-4"})]})]})}function rs({label:s,value:n,className:a,valueClassName:r}){return e.jsxs("div",{className:_("flex items-center py-1.5",a),children:[e.jsx("div",{className:"w-28 shrink-0 text-sm text-muted-foreground",children:s}),e.jsx("div",{className:_("text-sm",r),children:n||"-"})]})}function Ux({status:s}){const{t:n}=M("order"),a={[re.PENDING]:"bg-yellow-100 text-yellow-800 hover:bg-yellow-100",[re.PROCESSING]:"bg-blue-100 text-blue-800 hover:bg-blue-100",[re.CANCELLED]:"bg-red-100 text-red-800 hover:bg-red-100",[re.COMPLETED]:"bg-green-100 text-green-800 hover:bg-green-100",[re.DISCOUNTED]:"bg-gray-100 text-gray-800 hover:bg-gray-100"};return e.jsx(K,{variant:"secondary",className:_("font-medium",a[s]),children:n(`status.${re[s]}`)})}function Kx({id:s,trigger:n}){const[a,r]=m.useState(!1),[l,c]=m.useState(),{t:o}=M("order");return m.useEffect(()=>{(async()=>{if(a){const{data:x}=await td({id:s});c(x)}})()},[a,s]),e.jsxs(ve,{onOpenChange:r,open:a,children:[e.jsx(Ye,{asChild:!0,children:n}),e.jsxs(pe,{className:"max-w-xl",children:[e.jsxs(we,{className:"space-y-2",children:[e.jsx(be,{className:"text-lg font-medium",children:o("dialog.title")}),e.jsx("div",{className:"flex items-center justify-between text-sm",children:e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:"text-muted-foreground",children:[o("table.columns.tradeNo"),"：",l?.trade_no]}),l?.status&&e.jsx(Ux,{status:l.status})]})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"rounded-lg border p-4",children:[e.jsx("div",{className:"mb-2 text-sm font-medium",children:o("dialog.basicInfo")}),e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(rs,{label:o("dialog.fields.userEmail"),value:l?.user?.email?e.jsxs($s,{to:`/user/manage?email=${l.user.email}`,className:"group inline-flex items-center gap-1 text-primary hover:underline",children:[l.user.email,e.jsx(pr,{className:"h-3.5 w-3.5 opacity-0 transition-opacity group-hover:opacity-100"})]}):"-"}),e.jsx(rs,{label:o("dialog.fields.orderPeriod"),value:l&&o(`period.${l.period}`)}),e.jsx(rs,{label:o("dialog.fields.subscriptionPlan"),value:l?.plan?.name,valueClassName:"font-medium"}),e.jsx(rs,{label:o("dialog.fields.callbackNo"),value:l?.callback_no,valueClassName:"font-mono text-xs"})]})]}),e.jsxs("div",{className:"rounded-lg border p-4",children:[e.jsx("div",{className:"mb-2 text-sm font-medium",children:o("dialog.amountInfo")}),e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(rs,{label:o("dialog.fields.paymentAmount"),value:Fs(l?.total_amount||0),valueClassName:"font-medium text-primary"}),e.jsx(Se,{className:"my-2"}),e.jsx(rs,{label:o("dialog.fields.balancePayment"),value:Fs(l?.balance_amount||0)}),e.jsx(rs,{label:o("dialog.fields.discountAmount"),value:Fs(l?.discount_amount||0),valueClassName:"text-green-600"}),e.jsx(rs,{label:o("dialog.fields.refundAmount"),value:Fs(l?.refund_amount||0),valueClassName:"text-red-600"}),e.jsx(rs,{label:o("dialog.fields.deductionAmount"),value:Fs(l?.surplus_amount||0)})]})]}),e.jsxs("div",{className:"rounded-lg border p-4",children:[e.jsx("div",{className:"mb-2 text-sm font-medium",children:o("dialog.timeInfo")}),e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(rs,{label:o("dialog.fields.createdAt"),value:_e(l?.created_at),valueClassName:"font-mono text-xs"}),e.jsx(rs,{label:o("dialog.fields.updatedAt"),value:_e(l?.updated_at),valueClassName:"font-mono text-xs"})]})]})]})]})]})}const Bx={[is.NEW]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[is.RENEWAL]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[is.UPGRADE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[is.RESET_FLOW]:{color:"text-slate-700",bgColor:"bg-slate-100/80"}},Gx={[De.MONTH_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[De.QUARTER_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[De.HALF_YEAR_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[De.YEAR_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[De.TWO_YEAR_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[De.THREE_YEAR_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[De.ONETIME_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"},[De.RESET_PRICE]:{color:"text-slate-700",bgColor:"bg-slate-100/80"}},Wx=s=>re[s],Yx=s=>me[s],Qx=s=>is[s],Jx=s=>{const{t:n}=M("order");return[{accessorKey:"trade_no",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.tradeNo")}),cell:({row:a})=>{const r=a.original.trade_no,l=r.length>6?`${r.slice(0,3)}...${r.slice(-3)}`:r;return e.jsx("div",{className:"flex items-center",children:e.jsx(Kx,{trigger:e.jsxs(X,{variant:"ghost",size:"sm",className:"flex h-8 items-center gap-1.5 px-2 font-medium text-primary transition-colors hover:bg-primary/10 hover:text-primary/80",children:[e.jsx("span",{className:"font-mono",children:l}),e.jsx(pr,{className:"h-3.5 w-3.5 opacity-70"})]}),id:a.original.id})})},enableSorting:!1,enableHiding:!1},{accessorKey:"type",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.type")}),cell:({row:a})=>{const r=a.getValue("type"),l=Bx[r];return e.jsx(K,{variant:"secondary",className:_("font-medium transition-colors text-nowrap",l.color,l.bgColor,"border border-border/50","hover:bg-slate-200/80"),children:n(`type.${Qx(r)}`)})},enableSorting:!1,enableHiding:!1},{accessorKey:"plan.name",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.plan")}),cell:({row:a})=>e.jsx("div",{className:"flex space-x-2",children:e.jsx("span",{className:"max-w-32 truncate font-medium text-foreground/90 sm:max-w-72 md:max-w-[31rem]",children:a.original.plan?.name||"-"})}),enableSorting:!1,enableHiding:!1},{accessorKey:"period",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.period")}),cell:({row:a})=>{const r=a.getValue("period"),l=Gx[r];return e.jsx(K,{variant:"secondary",className:_("font-medium transition-colors text-nowrap",l.color,l.bgColor,"hover:bg-opacity-80"),children:n(`period.${r}`)})},enableSorting:!1,enableHiding:!1},{accessorKey:"total_amount",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.amount")}),cell:({row:a})=>{const r=a.getValue("total_amount"),l=typeof r=="number"?(r/100).toFixed(2):"N/A";return e.jsxs("div",{className:"flex items-center font-mono text-foreground/90",children:["¥",l]})},enableSorting:!0,enableHiding:!1},{accessorKey:"status",header:({column:a})=>e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(L,{column:a,title:n("table.columns.status")}),e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{children:e.jsx(Gr,{className:"h-4 w-4 text-muted-foreground/70 transition-colors hover:text-muted-foreground"})}),e.jsx(ce,{side:"top",className:"max-w-[200px] text-sm",children:n("status.tooltip")})]})})]}),cell:({row:a})=>{const r=Ys.find(l=>l.value===a.getValue("status"));return r?e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[r.icon&&e.jsx(r.icon,{className:`h-4 w-4 text-${r.color}`}),e.jsx("span",{className:"text-sm font-medium",children:n(`status.${Wx(r.value)}`)})]}),r.value===re.PENDING&&e.jsxs(Cs,{modal:!0,children:[e.jsx(Ss,{asChild:!0,children:e.jsxs(X,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-muted/60",children:[e.jsx(Ft,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:n("actions.openMenu")})]})}),e.jsxs(ps,{align:"end",className:"w-[140px]",children:[e.jsx(ge,{className:"cursor-pointer",onClick:async()=>{await ad({trade_no:a.original.trade_no}),s()},children:n("actions.markAsPaid")}),e.jsx(ge,{className:"cursor-pointer text-destructive focus:text-destructive",onClick:async()=>{await nd({trade_no:a.original.trade_no}),s()},children:n("actions.cancel")})]})]})]}):null},enableSorting:!0,enableHiding:!1},{accessorKey:"commission_balance",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.commission")}),cell:({row:a})=>{const r=a.getValue("commission_balance"),l=r?(r/100).toFixed(2):"-";return e.jsx("div",{className:"flex items-center font-mono text-foreground/90",children:r?`¥${l}`:"-"})},enableSorting:!0,enableHiding:!1},{accessorKey:"commission_status",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.commissionStatus")}),cell:({row:a})=>{const r=a.original.status,l=a.original.commission_balance,c=ut.find(o=>o.value===a.getValue("commission_status"));return l==0||!c?e.jsx("span",{className:"text-muted-foreground",children:"-"}):e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[c.icon&&e.jsx(c.icon,{className:`h-4 w-4 text-${c.color}`}),e.jsx("span",{className:"text-sm font-medium",children:n(`commission.${Yx(c.value)}`)})]}),c.value===me.PENDING&&r===re.COMPLETED&&e.jsxs(Cs,{modal:!0,children:[e.jsx(Ss,{asChild:!0,children:e.jsxs(X,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 hover:bg-muted/60",children:[e.jsx(Ft,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:n("actions.openMenu")})]})}),e.jsxs(ps,{align:"end",className:"w-[120px]",children:[e.jsx(ge,{className:"cursor-pointer",onClick:async()=>{await an({trade_no:a.original.trade_no,commission_status:me.PROCESSING}),s()},children:n("commission.PROCESSING")}),e.jsx(ge,{className:"cursor-pointer text-destructive focus:text-destructive",onClick:async()=>{await an({trade_no:a.original.trade_no,commission_status:me.INVALID}),s()},children:n("commission.INVALID")})]})]})]})},enableSorting:!0,enableHiding:!1},{accessorKey:"created_at",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.createdAt")}),cell:({row:a})=>e.jsx("div",{className:"text-nowrap font-mono text-sm text-muted-foreground",children:_e(a.getValue("created_at"),"YYYY/MM/DD HH:mm:ss")}),enableSorting:!0,enableHiding:!1}]};function Zx(){const[s]=fr(),[n,a]=m.useState({}),[r,l]=m.useState({}),[c,o]=m.useState([]),[u,x]=m.useState([]),[i,d]=m.useState({pageIndex:0,pageSize:20});m.useEffect(()=>{const g=Object.entries({user_id:"string",order_id:"string",commission_status:"number",status:"number",commission_balance:"string"}).map(([j,S])=>{const R=s.get(j);return R?{id:j,value:S==="number"?parseInt(R):R}:null}).filter(Boolean);g.length>0&&o(g)},[s]);const{refetch:f,data:w,isLoading:P}=ie({queryKey:["orderList",i,c,u],queryFn:()=>sd({pageSize:i.pageSize,current:i.pageIndex+1,filter:c,sort:u})}),C=ts({data:w?.data??[],columns:Jx(f),state:{sorting:u,columnVisibility:r,rowSelection:n,columnFilters:c,pagination:i},rowCount:w?.total??0,manualPagination:!0,manualFiltering:!0,manualSorting:!0,enableRowSelection:!0,onRowSelectionChange:a,onSortingChange:x,onColumnFiltersChange:o,onColumnVisibilityChange:l,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),onPaginationChange:d,getSortedRowModel:us(),getFacetedRowModel:Ts(),getFacetedUniqueValues:Ds(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(xs,{table:C,toolbar:e.jsx(Hx,{table:C,refetch:f}),showPagination:!0})}function Xx(){const{t:s}=M("order");return e.jsxs(Pe,{children:[e.jsxs(Ee,{children:[e.jsx(qe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")}),e.jsx("p",{className:"text-muted-foreground mt-2",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(Zx,{})})]})]})}const eh=Object.freeze(Object.defineProperty({__proto__:null,default:Xx},Symbol.toStringTag,{value:"Module"}));function sh({column:s,title:n,options:a}){const r=s?.getFacetedUniqueValues(),l=new Set(s?.getFilterValue());return e.jsxs(js,{children:[e.jsx(vs,{asChild:!0,children:e.jsxs(D,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(vt,{className:"mr-2 h-4 w-4"}),n,l?.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(Se,{orientation:"vertical",className:"mx-2 h-4"}),e.jsx(K,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:l.size}),e.jsx("div",{className:"hidden space-x-1 lg:flex",children:l.size>2?e.jsxs(K,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[l.size," selected"]}):a.filter(c=>l.has(c.value)).map(c=>e.jsx(K,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:c.label},c.value))})]})]})}),e.jsx(ms,{className:"w-[200px] p-0",align:"start",children:e.jsxs(Vs,{children:[e.jsx(Us,{placeholder:n}),e.jsxs(Ms,{children:[e.jsx(Ks,{children:"No results found."}),e.jsx(Ge,{children:a.map(c=>{const o=l.has(c.value);return e.jsxs(Ie,{onSelect:()=>{o?l.delete(c.value):l.add(c.value);const u=Array.from(l);s?.setFilterValue(u.length?u:void 0)},children:[e.jsx("div",{className:_("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",o?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(As,{className:_("h-4 w-4")})}),c.icon&&e.jsx(c.icon,{className:`mr-2 h-4 w-4 text-muted-foreground text-${c.color}`}),e.jsx("span",{children:c.label}),r?.get(c.value)&&e.jsx("span",{className:"ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs",children:r.get(c.value)})]},c.value)})}),l.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(at,{}),e.jsx(Ge,{children:e.jsx(Ie,{onSelect:()=>s?.setFilterValue(void 0),className:"justify-center text-center",children:"Clear filters"})})]})]})]})})]})}const th=h.object({id:h.coerce.number().nullable().optional(),name:h.string().min(1,"请输入优惠券名称"),code:h.string().nullable(),type:h.coerce.number(),value:h.coerce.number(),started_at:h.coerce.number(),ended_at:h.coerce.number(),limit_use:h.union([h.string(),h.number()]).nullable(),limit_use_with_user:h.union([h.string(),h.number()]).nullable(),generate_count:h.coerce.number().nullable().optional(),limit_plan_ids:h.array(h.coerce.number()).default([]).nullable(),limit_period:h.array(h.nativeEnum(xt)).default([]).nullable()}).refine(s=>s.ended_at>s.started_at,{message:"结束时间必须晚于开始时间",path:["ended_at"]}),xn={name:"",code:null,type:Ue.AMOUNT,value:0,started_at:Math.floor(Date.now()/1e3),ended_at:Math.floor(Date.now()/1e3)+7*24*60*60,limit_use:null,limit_use_with_user:null,limit_plan_ids:[],limit_period:[],generate_count:null};function al({defaultValues:s,refetch:n,type:a="create",dialogTrigger:r=null,open:l,onOpenChange:c}){const{t:o}=M("coupon"),[u,x]=m.useState(!1),i=l??u,d=c??x,[f,w]=m.useState([]),P=fe({resolver:ye(th),defaultValues:s||xn});m.useEffect(()=>{s&&P.reset(s)},[s,P]),m.useEffect(()=>{Hs().then(({data:j})=>w(j))},[]);const C=j=>{if(!j)return;const S=(R,E)=>{const F=new Date(E*1e3);return R.setHours(F.getHours(),F.getMinutes(),F.getSeconds()),Math.floor(R.getTime()/1e3)};j.from&&P.setValue("started_at",S(j.from,P.watch("started_at"))),j.to&&P.setValue("ended_at",S(j.to,P.watch("ended_at")))},p=async j=>{const S=await id(j);if(j.generate_count&&S){const R=new Blob([S],{type:"text/csv;charset=utf-8;"}),E=document.createElement("a");E.href=window.URL.createObjectURL(R),E.download=`coupons_${new Date().getTime()}.csv`,E.click(),window.URL.revokeObjectURL(E.href)}d(!1),a==="create"&&P.reset(xn),n()},g=(j,S)=>e.jsxs("div",{className:"flex-1 space-y-1.5",children:[e.jsx("div",{className:"text-sm font-medium text-muted-foreground",children:S}),e.jsx(T,{type:"datetime-local",step:"1",value:_e(P.watch(j),"YYYY-MM-DDTHH:mm:ss"),onChange:R=>{const E=new Date(R.target.value);P.setValue(j,Math.floor(E.getTime()/1e3))},className:"h-8 [&::-webkit-calendar-picker-indicator]:hidden"})]});return e.jsxs(ve,{open:i,onOpenChange:d,children:[r&&e.jsx(Ye,{asChild:!0,children:r}),e.jsxs(pe,{className:"sm:max-w-[425px]",children:[e.jsx(we,{children:e.jsx(be,{children:o(a==="create"?"form.add":"form.edit")})}),e.jsx(Ne,{...P,children:e.jsxs("form",{onSubmit:P.handleSubmit(p),className:"space-y-4",children:[e.jsx(b,{control:P.control,name:"name",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:o("form.name.label")}),e.jsx(T,{placeholder:o("form.name.placeholder"),...j}),e.jsx(k,{})]})}),a==="create"&&e.jsx(b,{control:P.control,name:"generate_count",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:o("form.generateCount.label")}),e.jsx(T,{type:"number",min:0,placeholder:o("form.generateCount.placeholder"),...j,value:j.value===void 0?"":j.value,onChange:S=>j.onChange(S.target.value===""?"":parseInt(S.target.value)),className:"h-9"}),e.jsx(z,{className:"text-xs",children:o("form.generateCount.description")}),e.jsx(k,{})]})}),(!P.watch("generate_count")||P.watch("generate_count")==null)&&e.jsx(b,{control:P.control,name:"code",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:o("form.code.label")}),e.jsx(T,{placeholder:o("form.code.placeholder"),...j,className:"h-9"}),e.jsx(z,{className:"text-xs",children:o("form.code.description")}),e.jsx(k,{})]})}),e.jsxs(v,{children:[e.jsx(y,{children:o("form.type.label")}),e.jsxs("div",{className:"flex",children:[e.jsx(b,{control:P.control,name:"type",render:({field:j})=>e.jsxs(J,{value:j.value.toString(),onValueChange:S=>{const R=j.value,E=parseInt(S);j.onChange(E);const F=P.getValues("value");F&&(R===Ue.AMOUNT&&E===Ue.PERCENTAGE?P.setValue("value",F/100):R===Ue.PERCENTAGE&&E===Ue.AMOUNT&&P.setValue("value",F*100))},children:[e.jsx(W,{className:"flex-[1.2] rounded-r-none border-r-0 focus:z-10",children:e.jsx(Z,{placeholder:o("form.type.placeholder")})}),e.jsx(Y,{children:Object.entries(gu).map(([S,R])=>e.jsx(q,{value:S,children:o(`table.toolbar.types.${S}`)},S))})]})}),e.jsx(b,{control:P.control,name:"value",render:({field:j})=>{const S=j.value==null?"":P.watch("type")===Ue.AMOUNT&&typeof j.value=="number"?(j.value/100).toString():j.value.toString();return e.jsx(T,{type:"number",placeholder:o("form.value.placeholder"),...j,value:S,onChange:R=>{const E=R.target.value;if(E===""){j.onChange("");return}const F=parseFloat(E);isNaN(F)||j.onChange(P.watch("type")===Ue.AMOUNT?Math.round(F*100):F)},step:"any",min:0,className:"flex-[2] rounded-none border-x-0 text-left"})}}),e.jsx("div",{className:"flex min-w-[40px] items-center justify-center rounded-md rounded-l-none border border-l-0 border-input bg-muted/50 px-3 font-medium text-muted-foreground",children:e.jsx("span",{children:P.watch("type")==Ue.AMOUNT?"¥":"%"})})]})]}),e.jsxs(v,{children:[e.jsx(y,{children:o("form.validity.label")}),e.jsxs(js,{children:[e.jsx(vs,{asChild:!0,children:e.jsxs(D,{variant:"outline",className:_("w-full justify-start text-left font-normal",!P.watch("started_at")&&"text-muted-foreground"),children:[e.jsx(jt,{className:"mr-2 h-4 w-4"}),_e(P.watch("started_at"),"YYYY-MM-DD HH:mm:ss")," ",o("form.validity.to")," ",_e(P.watch("ended_at"),"YYYY-MM-DD HH:mm:ss")]})}),e.jsxs(ms,{className:"w-auto p-0",align:"start",children:[e.jsx("div",{className:"border-b border-border",children:e.jsx(Bs,{mode:"range",selected:{from:new Date(P.watch("started_at")*1e3),to:new Date(P.watch("ended_at")*1e3)},onSelect:C,numberOfMonths:2})}),e.jsx("div",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-4",children:[g("started_at",o("table.validity.startTime")),e.jsx("div",{className:"mt-6 text-sm text-muted-foreground",children:o("form.validity.to")}),g("ended_at",o("table.validity.endTime"))]})})]})]}),e.jsx(k,{})]}),e.jsx(b,{control:P.control,name:"limit_use",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:o("form.limitUse.label")}),e.jsx(T,{type:"number",min:0,placeholder:o("form.limitUse.placeholder"),...j,value:j.value===null?"":j.value,onChange:S=>j.onChange(S.target.value===""?null:parseInt(S.target.value)),className:"h-9"}),e.jsx(z,{className:"text-xs",children:o("form.limitUse.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:P.control,name:"limit_use_with_user",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:o("form.limitUseWithUser.label")}),e.jsx(T,{type:"number",min:0,placeholder:o("form.limitUseWithUser.placeholder"),...j,value:j.value===null?"":j.value,onChange:S=>j.onChange(S.target.value===""?null:parseInt(S.target.value)),className:"h-9"}),e.jsx(z,{className:"text-xs",children:o("form.limitUseWithUser.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:P.control,name:"limit_period",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:o("form.limitPeriod.label")}),e.jsx(st,{options:Object.entries(xt).filter(([S])=>isNaN(Number(S))).map(([S,R])=>({label:o(`coupon:period.${R}`),value:S})),onChange:S=>{if(S.length===0){j.onChange([]);return}const R=S.map(E=>xt[E.value]);j.onChange(R)},value:(j.value||[]).map(S=>({label:o(`coupon:period.${S}`),value:Object.entries(xt).find(([R,E])=>E===S)?.[0]||""})),placeholder:o("form.limitPeriod.placeholder"),emptyIndicator:e.jsx("p",{className:"text-center text-sm text-muted-foreground",children:o("form.limitPeriod.empty")})}),e.jsx(z,{className:"text-xs",children:o("form.limitPeriod.description")}),e.jsx(k,{})]})}),e.jsx(b,{control:P.control,name:"limit_plan_ids",render:({field:j})=>e.jsxs(v,{children:[e.jsx(y,{children:o("form.limitPlan.label")}),e.jsx(st,{options:f?.map(S=>({label:S.name,value:S.id.toString()}))||[],onChange:S=>j.onChange(S.map(R=>Number(R.value))),value:(f||[]).filter(S=>(j.value||[]).includes(S.id)).map(S=>({label:S.name,value:S.id.toString()})),placeholder:o("form.limitPlan.placeholder"),emptyIndicator:e.jsx("p",{className:"text-center text-sm text-muted-foreground",children:o("form.limitPlan.empty")})}),e.jsx(k,{})]})}),e.jsx(Oe,{children:e.jsx(D,{type:"submit",disabled:P.formState.isSubmitting,children:P.formState.isSubmitting?o("form.submit.saving"):o("form.submit.save")})})]})})]})]})}function ah({table:s,refetch:n}){const a=s.getState().columnFilters.length>0,{t:r}=M("coupon");return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(al,{refetch:n,dialogTrigger:e.jsxs(D,{variant:"outline",size:"sm",className:"h-8 space-x-2",children:[e.jsx(Re,{icon:"ion:add"}),e.jsx("div",{children:r("form.add")})]})}),e.jsx(T,{placeholder:r("table.toolbar.search"),value:s.getColumn("name")?.getFilterValue()??"",onChange:l=>s.getColumn("name")?.setFilterValue(l.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),s.getColumn("type")&&e.jsx(sh,{column:s.getColumn("type"),title:r("table.toolbar.type"),options:[{value:Ue.AMOUNT,label:r(`table.toolbar.types.${Ue.AMOUNT}`)},{value:Ue.PERCENTAGE,label:r(`table.toolbar.types.${Ue.PERCENTAGE}`)}]}),a&&e.jsxs(D,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:[r("table.toolbar.reset"),e.jsx(Xe,{className:"ml-2 h-4 w-4"})]})]})}const nl=m.createContext(void 0);function nh({children:s,refetch:n}){const[a,r]=m.useState(!1),[l,c]=m.useState(null),o=x=>{c(x),r(!0)},u=()=>{r(!1),c(null)};return e.jsxs(nl.Provider,{value:{isOpen:a,currentCoupon:l,openEdit:o,closeEdit:u},children:[s,l&&e.jsx(al,{defaultValues:l,refetch:n,type:"edit",open:a,onOpenChange:r})]})}function rh(){const s=m.useContext(nl);if(s===void 0)throw new Error("useCouponEdit must be used within a CouponEditProvider");return s}const lh=s=>{const{t:n}=M("coupon");return[{accessorKey:"id",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.id")}),cell:({row:a})=>e.jsx(K,{children:a.original.id}),enableSorting:!0},{accessorKey:"show",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.show")}),cell:({row:a})=>e.jsx(Q,{defaultChecked:a.original.show,onCheckedChange:r=>{cd({id:a.original.id,show:r}).then(({data:l})=>!l&&s())}}),enableSorting:!1},{accessorKey:"name",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.name")}),cell:({row:a})=>e.jsx("div",{className:"flex items-center",children:e.jsx("span",{children:a.original.name})}),enableSorting:!1,size:800},{accessorKey:"type",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.type")}),cell:({row:a})=>e.jsx(K,{variant:"outline",children:n(`table.toolbar.types.${a.original.type}`)}),enableSorting:!0},{accessorKey:"code",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.code")}),cell:({row:a})=>e.jsx(K,{variant:"secondary",children:a.original.code}),enableSorting:!0},{accessorKey:"limit_use",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.limitUse")}),cell:({row:a})=>e.jsx(K,{variant:"outline",children:a.original.limit_use===null?n("table.validity.unlimited"):a.original.limit_use}),enableSorting:!0},{accessorKey:"limit_use_with_user",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.limitUseWithUser")}),cell:({row:a})=>e.jsx(K,{variant:"outline",children:a.original.limit_use_with_user===null?n("table.validity.noLimit"):a.original.limit_use_with_user}),enableSorting:!0},{accessorKey:"#",header:({column:a})=>e.jsx(L,{column:a,title:n("table.columns.validity")}),cell:({row:a})=>{const[r,l]=m.useState(!1),c=Date.now(),o=a.original.started_at*1e3,u=a.original.ended_at*1e3,x=c>u,i=c<o,d=Math.ceil((u-c)/(1e3*60*60*24)),w=x?{label:n("table.validity.expired",{days:Math.abs(d)}),color:"bg-red-50 text-red-600 dark:bg-red-500/10 dark:text-red-400"}:i?{label:n("table.validity.notStarted",{days:Math.abs(Math.ceil((o-c)/(1e3*60*60*24)))}),color:"bg-yellow-50 text-yellow-600 dark:bg-yellow-500/10 dark:text-yellow-400"}:{label:n("table.validity.remaining",{days:d}),color:"bg-green-50 text-green-600 dark:bg-green-500/10 dark:text-green-400"};return e.jsxs(Rr,{open:r,onOpenChange:l,children:[e.jsx(Ir,{asChild:!0,children:e.jsxs("div",{className:"group -m-0.5 flex max-w-[280px] cursor-pointer items-center gap-2 rounded-md p-0.5 transition-colors hover:bg-muted/40",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx("div",{className:_("whitespace-nowrap rounded-md px-1.5 py-0.5 text-xs font-medium",w.color),children:w.label}),e.jsxs("div",{className:"flex min-w-0 items-center gap-1 text-muted-foreground",children:[e.jsx("div",{className:"truncate text-xs",children:_e(a.original.started_at,"MM/DD HH:mm")}),e.jsx("div",{className:"shrink-0 opacity-30",children:"→"}),e.jsx("div",{className:"truncate text-xs",children:_e(a.original.ended_at,"MM/DD HH:mm")})]})]}),e.jsx(Bo,{className:_("h-3.5 w-3.5 shrink-0 text-muted-foreground/50 transition-transform duration-200",r&&"rotate-180")})]})}),e.jsx(Vr,{children:e.jsx("div",{className:"px-0.5 pb-0.5 pt-1.5",children:e.jsxs("div",{className:"space-y-1.5 border-l-2 border-muted pl-3 text-xs text-muted-foreground",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:n("table.validity.startTime")}),e.jsx("span",{className:"font-medium text-foreground",children:_e(a.original.started_at,"YYYY/MM/DD HH:mm")})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:n("table.validity.endTime")}),e.jsx("span",{className:"font-medium text-foreground",children:_e(a.original.ended_at,"YYYY/MM/DD HH:mm")})]})]})})})]})},enableSorting:!1,size:8e3},{id:"actions",header:({column:a})=>e.jsx(L,{className:"justify-end",column:a,title:n("table.columns.actions")}),cell:({row:a})=>{const{openEdit:r}=rh();return e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-muted",onClick:()=>r(a.original),children:[e.jsx(qs,{className:"h-4 w-4 text-muted-foreground hover:text-foreground"}),e.jsx("span",{className:"sr-only",children:n("table.actions.edit")})]}),e.jsx(We,{title:n("table.actions.deleteConfirm.title"),description:n("table.actions.deleteConfirm.description"),confirmText:n("table.actions.deleteConfirm.confirmText"),variant:"destructive",onConfirm:async()=>{od({id:a.original.id}).then(({data:l})=>{l&&(A.success("删除成功"),s())})},children:e.jsxs(D,{variant:"ghost",size:"icon",className:"h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900",children:[e.jsx(os,{className:"h-4 w-4 text-muted-foreground hover:text-red-600 dark:hover:text-red-400"}),e.jsx("span",{className:"sr-only",children:n("table.actions.delete")})]})})]})}}]};function ih(){const[s,n]=m.useState({}),[a,r]=m.useState({}),[l,c]=m.useState([]),[o,u]=m.useState([]),[x,i]=m.useState({pageIndex:0,pageSize:20}),{refetch:d,data:f}=ie({queryKey:["couponList",x,l,o],queryFn:()=>ld({pageSize:x.pageSize,current:x.pageIndex+1,filter:l,sort:o})}),w=ts({data:f?.data??[],columns:lh(d),state:{sorting:o,columnVisibility:a,rowSelection:s,columnFilters:l,pagination:x},pageCount:Math.ceil((f?.total??0)/x.pageSize),rowCount:f?.total??0,manualPagination:!0,manualFiltering:!0,manualSorting:!0,enableRowSelection:!0,onRowSelectionChange:n,onSortingChange:u,onColumnFiltersChange:c,onColumnVisibilityChange:r,onPaginationChange:i,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),getSortedRowModel:us(),getFacetedRowModel:Ts(),getFacetedUniqueValues:Ds(),initialState:{columnPinning:{right:["actions"]}}});return e.jsx(nh,{refetch:d,children:e.jsx("div",{className:"space-y-4",children:e.jsx(xs,{table:w,toolbar:e.jsx(ah,{table:w,refetch:d})})})})}function oh(){const{t:s}=M("coupon");return e.jsxs(Pe,{children:[e.jsxs(Ee,{children:[e.jsx(qe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")}),e.jsx("p",{className:"text-muted-foreground mt-2",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(ih,{})})]})]})}const ch=Object.freeze(Object.defineProperty({__proto__:null,default:oh},Symbol.toStringTag,{value:"Module"})),dh=1,uh=1e6;let ma=0;function mh(){return ma=(ma+1)%Number.MAX_SAFE_INTEGER,ma.toString()}const xa=new Map,hn=s=>{if(xa.has(s))return;const n=setTimeout(()=>{xa.delete(s),ht({type:"REMOVE_TOAST",toastId:s})},uh);xa.set(s,n)},xh=(s,n)=>{switch(n.type){case"ADD_TOAST":return{...s,toasts:[n.toast,...s.toasts].slice(0,dh)};case"UPDATE_TOAST":return{...s,toasts:s.toasts.map(a=>a.id===n.toast.id?{...a,...n.toast}:a)};case"DISMISS_TOAST":{const{toastId:a}=n;return a?hn(a):s.toasts.forEach(r=>{hn(r.id)}),{...s,toasts:s.toasts.map(r=>r.id===a||a===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return n.toastId===void 0?{...s,toasts:[]}:{...s,toasts:s.toasts.filter(a=>a.id!==n.toastId)}}},Rt=[];let It={toasts:[]};function ht(s){It=xh(It,s),Rt.forEach(n=>{n(It)})}function hh({...s}){const n=mh(),a=l=>ht({type:"UPDATE_TOAST",toast:{...l,id:n}}),r=()=>ht({type:"DISMISS_TOAST",toastId:n});return ht({type:"ADD_TOAST",toast:{...s,id:n,open:!0,onOpenChange:l=>{l||r()}}}),{id:n,dismiss:r,update:a}}function rl(){const[s,n]=m.useState(It);return m.useEffect(()=>(Rt.push(n),()=>{const a=Rt.indexOf(n);a>-1&&Rt.splice(a,1)}),[s]),{...s,toast:hh,dismiss:a=>ht({type:"DISMISS_TOAST",toastId:a})}}function ph({open:s,onOpenChange:n,table:a}){const{t:r}=M("user"),{toast:l}=rl(),[c,o]=m.useState(!1),[u,x]=m.useState(""),[i,d]=m.useState(""),f=async()=>{if(!u||!i){l({title:r("messages.error"),description:r("messages.send_mail.required_fields"),variant:"destructive"});return}try{o(!0),await Lt.sendMail({subject:u,content:i,filter:a.getState().columnFilters,sort:a.getState().sorting[0]?.id,sort_type:a.getState().sorting[0]?.desc?"DESC":"ASC"}),l({title:r("messages.success"),description:r("messages.send_mail.success")}),n(!1),x(""),d("")}catch{l({title:r("messages.error"),description:r("messages.send_mail.failed"),variant:"destructive"})}finally{o(!1)}};return e.jsx(ve,{open:s,onOpenChange:n,children:e.jsxs(pe,{className:"sm:max-w-[500px]",children:[e.jsxs(we,{children:[e.jsx(be,{children:r("send_mail.title")}),e.jsx(ke,{children:r("send_mail.description")})]}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx("label",{htmlFor:"subject",className:"text-right",children:r("send_mail.subject")}),e.jsx(T,{id:"subject",value:u,onChange:w=>x(w.target.value),className:"col-span-3"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx("label",{htmlFor:"content",className:"text-right",children:r("send_mail.content")}),e.jsx(bs,{id:"content",value:i,onChange:w=>d(w.target.value),className:"col-span-3",rows:6})]})]}),e.jsx(Oe,{children:e.jsx(X,{type:"submit",onClick:f,disabled:c,children:r(c?"send_mail.sending":"send_mail.send")})})]})})}const fh=h.object({email_prefix:h.string().optional(),email_suffix:h.string().min(1),password:h.string().optional(),expired_at:h.number().optional().nullable(),plan_id:h.number().nullable(),generate_count:h.number().optional().nullable()}).refine(s=>s.generate_count===null?s.email_prefix!==void 0&&s.email_prefix!=="":!0,{message:"Email prefix is required when generate_count is null",path:["email_prefix"]}),gh={email_prefix:"",email_suffix:"",password:"",expired_at:null,plan_id:null,generate_count:void 0};function jh({refetch:s}){const{t:n}=M("user"),[a,r]=m.useState(!1),l=fe({resolver:ye(fh),defaultValues:gh,mode:"onChange"}),[c,o]=m.useState([]);return m.useEffect(()=>{a&&Hs().then(({data:u})=>{u&&o(u)})},[a]),e.jsxs(ve,{open:a,onOpenChange:r,children:[e.jsx(Ye,{asChild:!0,children:e.jsxs(X,{size:"sm",variant:"outline",className:"space-x-2 gap-0",children:[e.jsx(Re,{icon:"ion:add"}),e.jsx("div",{children:n("generate.button")})]})}),e.jsxs(pe,{className:"sm:max-w-[425px]",children:[e.jsxs(we,{children:[e.jsx(be,{children:n("generate.title")}),e.jsx(ke,{})]}),e.jsxs(Ne,{...l,children:[e.jsxs(v,{children:[e.jsx(y,{children:n("generate.form.email")}),e.jsxs("div",{className:"flex",children:[!l.watch("generate_count")&&e.jsx(b,{control:l.control,name:"email_prefix",render:({field:u})=>e.jsx(T,{className:"flex-[5] rounded-r-none",placeholder:n("generate.form.email_prefix"),...u})}),e.jsx("div",{className:`z-[-1] border border-r-0 border-input px-3 py-1 shadow-sm ${l.watch("generate_count")?"rounded-l-md":"border-l-0"}`,children:"@"}),e.jsx(b,{control:l.control,name:"email_suffix",render:({field:u})=>e.jsx(T,{className:"flex-[4] rounded-l-none",placeholder:n("generate.form.email_domain"),...u})})]})]}),e.jsx(b,{control:l.control,name:"password",render:({field:u})=>e.jsxs(v,{children:[e.jsx(y,{children:n("generate.form.password")}),e.jsx(T,{placeholder:n("generate.form.password_placeholder"),...u}),e.jsx(k,{})]})}),e.jsx(b,{control:l.control,name:"expired_at",render:({field:u})=>e.jsxs(v,{className:"flex flex-col",children:[e.jsx(y,{children:n("generate.form.expire_time")}),e.jsxs(js,{children:[e.jsx(vs,{asChild:!0,children:e.jsx(N,{children:e.jsxs(X,{variant:"outline",className:_("w-full pl-3 text-left font-normal",!u.value&&"text-muted-foreground"),children:[u.value?_e(u.value):e.jsx("span",{children:n("generate.form.expire_time_placeholder")}),e.jsx(jt,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),e.jsxs(ms,{className:"flex w-auto flex-col space-y-2 p-2",children:[e.jsx(Go,{asChild:!0,children:e.jsx(X,{variant:"outline",className:"w-full",onClick:()=>{u.onChange(null)},children:n("generate.form.permanent")})}),e.jsx("div",{className:"rounded-md border",children:e.jsx(Bs,{mode:"single",selected:u.value?new Date(u.value*1e3):void 0,onSelect:x=>{x&&u.onChange(x?.getTime()/1e3)}})})]})]})]})}),e.jsx(b,{control:l.control,name:"plan_id",render:({field:u})=>e.jsxs(v,{children:[e.jsx(y,{children:n("generate.form.subscription")}),e.jsx(N,{children:e.jsxs(J,{value:u.value?u.value.toString():"null",onValueChange:x=>u.onChange(x==="null"?null:parseInt(x)),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:n("generate.form.subscription_none")})}),e.jsxs(Y,{children:[e.jsx(q,{value:"null",children:n("generate.form.subscription_none")}),c.map(x=>e.jsx(q,{value:x.id.toString(),children:x.name},x.id))]})]})})]})}),!l.watch("email_prefix")&&e.jsx(b,{control:l.control,name:"generate_count",render:({field:u})=>e.jsxs(v,{children:[e.jsx(y,{children:n("generate.form.generate_count")}),e.jsx(T,{type:"number",placeholder:n("generate.form.generate_count_placeholder"),value:u.value||"",onChange:x=>u.onChange(x.target.value?parseInt(x.target.value):null)})]})})]}),e.jsxs(Oe,{children:[e.jsx(X,{variant:"outline",onClick:()=>r(!1),children:n("generate.form.cancel")}),e.jsx(X,{onClick:()=>l.handleSubmit(u=>{xd(u).then(({data:x})=>{x&&(A.success(n("generate.form.success")),l.reset(),s(),r(!1))})})(),children:n("generate.form.submit")})]})]})]})}const ll=Nn,il=_n,vh=wn,ol=m.forwardRef(({className:s,...n},a)=>e.jsx($t,{className:_("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...n,ref:a}));ol.displayName=$t.displayName;const bh=Ls("fixed overflow-y-scroll  z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-300 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-md",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-md"}},defaultVariants:{side:"right"}}),$a=m.forwardRef(({side:s="right",className:n,children:a,...r},l)=>e.jsxs(vh,{children:[e.jsx(ol,{}),e.jsxs(qt,{ref:l,className:_(bh({side:s}),n),...r,children:[e.jsxs(Sa,{className:"absolute right-4 top-4  rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[e.jsx(Xe,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]}),a]})]}));$a.displayName=qt.displayName;const qa=({className:s,...n})=>e.jsx("div",{className:_("flex flex-col space-y-2 text-center sm:text-left",s),...n});qa.displayName="SheetHeader";const cl=({className:s,...n})=>e.jsx("div",{className:_("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...n});cl.displayName="SheetFooter";const Ha=m.forwardRef(({className:s,...n},a)=>e.jsx(Ht,{ref:a,className:_("text-lg font-semibold text-foreground",s),...n}));Ha.displayName=Ht.displayName;const Ua=m.forwardRef(({className:s,...n},a)=>e.jsx(Ut,{ref:a,className:_("text-sm text-muted-foreground",s),...n}));Ua.displayName=Ut.displayName;function yh({table:s,refetch:n,permissionGroups:a=[],subscriptionPlans:r=[]}){const{t:l}=M("user"),{toast:c}=rl(),o=s.getState().columnFilters.length>0,[u,x]=m.useState([]),[i,d]=m.useState(!1),[f,w]=m.useState(!1),[P,C]=m.useState(!1),[p,g]=m.useState(!1),j=async()=>{try{const G=await Lt.dumpCSV({filter:s.getState().columnFilters,sort:s.getState().sorting[0]?.id,sort_type:s.getState().sorting[0]?.desc?"DESC":"ASC"}),ae=G;console.log(G);const $=new Blob([ae],{type:"text/csv;charset=utf-8;"}),I=window.URL.createObjectURL($),B=document.createElement("a");B.href=I,B.setAttribute("download",`users_${new Date().toISOString()}.csv`),document.body.appendChild(B),B.click(),B.remove(),window.URL.revokeObjectURL(I),c({title:l("messages.success"),description:l("messages.export.success")})}catch{c({title:l("messages.error"),description:l("messages.export.failed"),variant:"destructive"})}},S=async()=>{try{g(!0),await Lt.batchBan({filter:s.getState().columnFilters,sort:s.getState().sorting[0]?.id,sort_type:s.getState().sorting[0]?.desc?"DESC":"ASC"}),c({title:l("messages.success"),description:l("messages.batch_ban.success")}),n()}catch{c({title:l("messages.error"),description:l("messages.batch_ban.failed"),variant:"destructive"})}finally{g(!1),C(!1)}},R=[{label:l("filter.fields.email"),value:"email",type:"text",operators:[{label:l("filter.operators.contains"),value:"contains"},{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.id"),value:"id",type:"number",operators:[{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.plan_id"),value:"plan_id",type:"select",operators:[{label:l("filter.operators.eq"),value:"eq"}],useOptions:!0},{label:l("filter.fields.transfer_enable"),value:"transfer_enable",type:"number",unit:"GB",operators:[{label:l("filter.operators.gt"),value:"gt"},{label:l("filter.operators.lt"),value:"lt"},{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.total_used"),value:"total_used",type:"number",unit:"GB",operators:[{label:l("filter.operators.gt"),value:"gt"},{label:l("filter.operators.lt"),value:"lt"},{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.online_count"),value:"online_count",type:"number",operators:[{label:l("filter.operators.eq"),value:"eq"},{label:l("filter.operators.gt"),value:"gt"},{label:l("filter.operators.lt"),value:"lt"}]},{label:l("filter.fields.expired_at"),value:"expired_at",type:"date",operators:[{label:l("filter.operators.lt"),value:"lt"},{label:l("filter.operators.gt"),value:"gt"},{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.uuid"),value:"uuid",type:"text",operators:[{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.token"),value:"token",type:"text",operators:[{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.banned"),value:"banned",type:"select",operators:[{label:l("filter.operators.eq"),value:"eq"}],options:[{label:l("filter.status.normal"),value:"0"},{label:l("filter.status.banned"),value:"1"}]},{label:l("filter.fields.remark"),value:"remarks",type:"text",operators:[{label:l("filter.operators.contains"),value:"contains"},{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.inviter_email"),value:"invite_user.email",type:"text",operators:[{label:l("filter.operators.contains"),value:"contains"},{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.invite_user_id"),value:"invite_user_id",type:"number",operators:[{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.is_admin"),value:"is_admin",type:"boolean",operators:[{label:l("filter.operators.eq"),value:"eq"}]},{label:l("filter.fields.is_staff"),value:"is_staff",type:"boolean",operators:[{label:l("filter.operators.eq"),value:"eq"}]}],E=G=>G*1024*1024*1024,F=G=>G/(1024*1024*1024),H=()=>{x([...u,{field:"",operator:"",value:""}])},te=G=>{x(u.filter((ae,$)=>$!==G))},O=(G,ae,$)=>{const I=[...u];if(I[G]={...I[G],[ae]:$},ae==="field"){const B=R.find(ne=>ne.value===$);B&&(I[G].operator=B.operators[0].value,I[G].value=B.type==="boolean"?!1:"")}x(I)},se=(G,ae)=>{const $=R.find(I=>I.value===G.field);if(!$)return null;switch($.type){case"text":return e.jsx(T,{placeholder:l("filter.sheet.value"),value:G.value,onChange:I=>O(ae,"value",I.target.value)});case"number":return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(T,{type:"number",placeholder:l("filter.sheet.value_number",{unit:$.unit}),value:$.unit==="GB"?F(G.value||0):G.value,onChange:I=>{const B=Number(I.target.value);O(ae,"value",$.unit==="GB"?E(B):B)}}),$.unit&&e.jsx("span",{className:"text-sm text-muted-foreground",children:$.unit})]});case"date":return e.jsx(Bs,{mode:"single",selected:G.value,onSelect:I=>O(ae,"value",I),className:"rounded-md border"});case"select":return e.jsxs(J,{value:G.value,onValueChange:I=>O(ae,"value",I),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:l("filter.sheet.value")})}),e.jsx(Y,{children:$.useOptions?r.map(I=>e.jsx(q,{value:I.value.toString(),children:I.label},I.value)):$.options?.map(I=>e.jsx(q,{value:I.value.toString(),children:I.label},I.value))})]});case"boolean":return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Q,{checked:G.value,onCheckedChange:I=>O(ae,"value",I)}),e.jsx(Ot,{children:G.value?l("filter.boolean.true"):l("filter.boolean.false")})]});default:return null}},He=()=>{const G=u.filter(ae=>ae.field&&ae.operator&&ae.value!=="").map(ae=>{const $=R.find(B=>B.value===ae.field);let I=ae.value;return ae.operator==="contains"?{id:ae.field,value:I}:($?.type==="date"&&I instanceof Date&&(I=Math.floor(I.getTime()/1e3)),$?.type==="boolean"&&(I=I?1:0),{id:ae.field,value:`${ae.operator}:${I}`})});s.setColumnFilters(G),d(!1)};return e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[e.jsxs("div",{className:"flex flex-1 flex-wrap items-center gap-2",children:[e.jsx(jh,{refetch:n}),e.jsx(T,{placeholder:l("filter.email_search"),value:s.getColumn("email")?.getFilterValue()??"",onChange:G=>s.getColumn("email")?.setFilterValue(G.target.value),className:"h-8 w-[150px] lg:w-[250px]"}),e.jsxs(ll,{open:i,onOpenChange:d,children:[e.jsx(il,{asChild:!0,children:e.jsxs(D,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(Wo,{className:"mr-2 h-4 w-4"}),l("filter.advanced"),u.length>0&&e.jsx(K,{variant:"secondary",className:"ml-2 rounded-sm px-1",children:u.length})]})}),e.jsxs($a,{className:"w-[400px] sm:w-[540px]",children:[e.jsxs(qa,{children:[e.jsx(Ha,{children:l("filter.sheet.title")}),e.jsx(Ua,{children:l("filter.sheet.description")})]}),e.jsxs("div",{className:"mt-6 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"font-medium",children:l("filter.sheet.conditions")}),e.jsx(D,{variant:"outline",size:"sm",onClick:H,children:l("filter.sheet.add")})]}),e.jsx(zs,{className:"h-[calc(100vh-280px)] pr-4",children:e.jsx("div",{className:"space-y-4",children:u.map((G,ae)=>e.jsxs("div",{className:"space-y-3 rounded-lg border p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(Ot,{children:l("filter.sheet.condition",{number:ae+1})}),e.jsx(D,{variant:"ghost",size:"sm",onClick:()=>te(ae),children:e.jsx(Xe,{className:"h-4 w-4"})})]}),e.jsxs(J,{value:G.field,onValueChange:$=>O(ae,"field",$),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:l("filter.sheet.field")})}),e.jsx(Y,{children:R.map($=>e.jsx(q,{value:$.value,children:$.label},$.value))})]}),G.field&&e.jsxs(J,{value:G.operator,onValueChange:$=>O(ae,"operator",$),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:l("filter.sheet.operator")})}),e.jsx(Y,{children:R.find($=>$.value===G.field)?.operators.map($=>e.jsx(q,{value:$.value,children:$.label},$.value))})]}),G.field&&G.operator&&se(G,ae)]},ae))})}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(D,{variant:"outline",onClick:()=>{x([]),d(!1)},children:l("filter.sheet.reset")}),e.jsx(D,{onClick:He,children:l("filter.sheet.apply")})]})]})]})]}),o&&e.jsxs(D,{variant:"ghost",onClick:()=>{s.resetColumnFilters(),x([])},className:"h-8 px-2 lg:px-3",children:[l("reset"),e.jsx(Xe,{className:"ml-2 h-4 w-4"})]}),e.jsxs(Cs,{children:[e.jsx(Ss,{asChild:!0,children:e.jsx(D,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:l("actions.title")})}),e.jsxs(ps,{children:[e.jsx(ge,{onClick:()=>w(!0),children:l("actions.send_email")}),e.jsx(ge,{onClick:j,children:l("actions.export_csv")}),e.jsx(et,{}),e.jsx(ge,{onClick:()=>C(!0),className:"text-red-600 focus:text-red-600",children:l("actions.batch_ban")})]})]})]}),e.jsx(ph,{open:f,onOpenChange:w,table:s}),e.jsx(za,{open:P,onOpenChange:C,children:e.jsxs(Qt,{children:[e.jsxs(Jt,{children:[e.jsx(Xt,{children:l("actions.confirm_ban.title")}),e.jsx(ea,{children:l(o?"actions.confirm_ban.filtered_description":"actions.confirm_ban.all_description")})]}),e.jsxs(Zt,{children:[e.jsx(ta,{disabled:p,children:l("actions.confirm_ban.cancel")}),e.jsx(sa,{onClick:S,disabled:p,className:"bg-red-600 hover:bg-red-700 focus:ring-red-600",children:l(p?"actions.confirm_ban.banning":"actions.confirm_ban.confirm")})]})]})})]})}const Nh=h.object({id:h.number(),email:h.string().email(),invite_user_email:h.string().email().nullable().optional(),password:h.string().optional().nullable(),balance:h.coerce.number(),commission_balance:h.coerce.number(),u:h.number(),d:h.number(),transfer_enable:h.number(),expired_at:h.number().nullable(),plan_id:h.number().nullable(),banned:h.number(),commission_type:h.number(),commission_rate:h.number().nullable(),discount:h.number().nullable(),speed_limit:h.number().nullable(),device_limit:h.number().nullable(),is_admin:h.number(),is_staff:h.number(),remarks:h.string().nullable()}),dl=m.createContext(void 0);function _h({children:s,defaultValues:n,open:a,onOpenChange:r}){const[l,c]=m.useState(!1),[o,u]=m.useState(!1),[x,i]=m.useState([]),d=fe({resolver:ye(Nh),defaultValues:n,mode:"onChange"});m.useEffect(()=>{a!==void 0&&c(a)},[a]);const f=w=>{c(w),r?.(w)};return e.jsx(dl.Provider,{value:{form:d,formOpen:l,setFormOpen:f,datePickerOpen:o,setDatePickerOpen:u,planList:x,setPlanList:i},children:s})}function wh(){const s=m.useContext(dl);if(!s)throw new Error("useUserForm must be used within a UserFormProvider");return s}function Ch({refetch:s}){const{t:n}=M("user"),{form:a,formOpen:r,setFormOpen:l,datePickerOpen:c,setDatePickerOpen:o,planList:u,setPlanList:x}=wh();return m.useEffect(()=>{r&&Hs().then(({data:i})=>{x(i)})},[r,x]),e.jsxs(Ne,{...a,children:[e.jsx(b,{control:a.control,name:"email",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.email")}),e.jsx(N,{children:e.jsx(T,{...i,placeholder:n("edit.form.email_placeholder")})}),e.jsx(k,{...i})]})}),e.jsx(b,{control:a.control,name:"invite_user_email",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.inviter_email")}),e.jsx(N,{children:e.jsx(T,{value:i.value||"",onChange:d=>i.onChange(d.target.value?d.target.value:null),placeholder:n("edit.form.inviter_email_placeholder")})}),e.jsx(k,{...i})]})}),e.jsx(b,{control:a.control,name:"password",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.password")}),e.jsx(N,{children:e.jsx(T,{value:i.value||"",onChange:i.onChange,placeholder:n("edit.form.password_placeholder")})}),e.jsx(k,{...i})]})}),e.jsxs("div",{className:"grid gap-2 md:grid-cols-2",children:[e.jsx(b,{control:a.control,name:"balance",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.balance")}),e.jsx(N,{children:e.jsxs("div",{className:"flex",children:[e.jsx(T,{type:"number",value:i.value||"",onChange:i.onChange,placeholder:n("edit.form.balance_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"¥"})]})}),e.jsx(k,{...i})]})}),e.jsx(b,{control:a.control,name:"commission_balance",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.commission_balance")}),e.jsx(N,{children:e.jsxs("div",{className:"flex",children:[e.jsx(T,{type:"number",value:i.value||"",onChange:i.onChange,placeholder:n("edit.form.commission_balance_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"¥"})]})}),e.jsx(k,{...i})]})}),e.jsx(b,{control:a.control,name:"u",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.upload")}),e.jsx(N,{children:e.jsxs("div",{className:"flex",children:[e.jsx(T,{value:i.value/1024/1024/1024||"",onChange:d=>i.onChange(parseInt(d.target.value)*1024*1024*1024),placeholder:n("edit.form.upload_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"GB"})]})}),e.jsx(k,{...i})]})}),e.jsx(b,{control:a.control,name:"d",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.download")}),e.jsx(N,{children:e.jsxs("div",{className:"flex",children:[e.jsx(T,{type:"number",value:i.value/1024/1024/1024||"",onChange:d=>i.onChange(parseInt(d.target.value)*1024*1024*1024),placeholder:n("edit.form.download_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"GB"})]})}),e.jsx(k,{...i})]})})]}),e.jsx(b,{control:a.control,name:"transfer_enable",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.total_traffic")}),e.jsx(N,{children:e.jsxs("div",{className:"flex",children:[e.jsx(T,{type:"number",value:i.value/1024/1024/1024||"",onChange:d=>i.onChange(parseInt(d.target.value)*1024*1024*1024),placeholder:n("edit.form.total_traffic_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"GB"})]})}),e.jsx(k,{})]})}),e.jsx(b,{control:a.control,name:"expired_at",render:({field:i})=>e.jsxs(v,{className:"flex flex-col",children:[e.jsx(y,{children:n("edit.form.expire_time")}),e.jsxs(js,{open:c,onOpenChange:o,children:[e.jsx(vs,{asChild:!0,children:e.jsx(N,{children:e.jsxs(D,{type:"button",variant:"outline",className:_("w-full pl-3 text-left font-normal",!i.value&&"text-muted-foreground"),onClick:()=>o(!0),children:[i.value?_e(i.value):e.jsx("span",{children:n("edit.form.expire_time_placeholder")}),e.jsx(jt,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),e.jsx(ms,{className:"w-auto p-0",align:"start",side:"top",sideOffset:4,onInteractOutside:d=>{d.preventDefault()},onEscapeKeyDown:d=>{d.preventDefault()},children:e.jsxs("div",{className:"flex flex-col space-y-3 p-3",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(D,{type:"button",variant:"outline",className:"flex-1",onClick:()=>{i.onChange(null),o(!1)},children:n("edit.form.expire_time_permanent")}),e.jsx(D,{type:"button",variant:"outline",className:"flex-1",onClick:()=>{const d=new Date;d.setMonth(d.getMonth()+1),d.setHours(23,59,59,999),i.onChange(Math.floor(d.getTime()/1e3)),o(!1)},children:n("edit.form.expire_time_1month")}),e.jsx(D,{type:"button",variant:"outline",className:"flex-1",onClick:()=>{const d=new Date;d.setMonth(d.getMonth()+3),d.setHours(23,59,59,999),i.onChange(Math.floor(d.getTime()/1e3)),o(!1)},children:n("edit.form.expire_time_3months")})]}),e.jsx("div",{className:"rounded-md border",children:e.jsx(Bs,{mode:"single",selected:i.value?new Date(i.value*1e3):void 0,onSelect:d=>{if(d){const f=new Date(i.value?i.value*1e3:Date.now());d.setHours(f.getHours(),f.getMinutes(),f.getSeconds()),i.onChange(Math.floor(d.getTime()/1e3))}},disabled:d=>d<new Date,initialFocus:!0,className:"rounded-md border-none"})}),e.jsxs("div",{className:"space-y-1.5",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm font-medium text-muted-foreground",children:n("edit.form.expire_time_specific")}),e.jsx(D,{type:"button",variant:"ghost",size:"sm",onClick:()=>{const d=new Date;d.setHours(23,59,59,999),i.onChange(Math.floor(d.getTime()/1e3))},className:"h-6 px-2 text-xs",children:n("edit.form.expire_time_today")})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(T,{type:"datetime-local",step:"1",value:_e(i.value,"YYYY-MM-DDTHH:mm:ss"),onChange:d=>{const f=new Date(d.target.value);isNaN(f.getTime())||i.onChange(Math.floor(f.getTime()/1e3))},className:"flex-1"}),e.jsx(D,{type:"button",variant:"outline",onClick:()=>o(!1),children:n("edit.form.expire_time_confirm")})]})]})]})})]}),e.jsx(k,{})]})}),e.jsx(b,{control:a.control,name:"plan_id",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.subscription")}),e.jsx(N,{children:e.jsxs(J,{value:i.value?i.value.toString():"null",onValueChange:d=>i.onChange(d==="null"?null:parseInt(d)),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:n("edit.form.subscription_none")})}),e.jsxs(Y,{children:[e.jsx(q,{value:"null",children:n("edit.form.subscription_none")}),u.map(d=>e.jsx(q,{value:d.id.toString(),children:d.name},d.id))]})]})})]})}),e.jsx(b,{control:a.control,name:"banned",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.account_status")}),e.jsx(N,{children:e.jsxs(J,{value:i.value.toString(),onValueChange:d=>i.onChange(parseInt(d)),children:[e.jsx(W,{children:e.jsx(Z,{})}),e.jsxs(Y,{children:[e.jsx(q,{value:"1",children:n("columns.status_text.banned")}),e.jsx(q,{value:"0",children:n("columns.status_text.normal")})]})]})})]})}),e.jsx(b,{control:a.control,name:"commission_type",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.commission_type")}),e.jsx(N,{children:e.jsxs(J,{value:i.value.toString(),onValueChange:d=>i.onChange(parseInt(d)),children:[e.jsx(W,{children:e.jsx(Z,{placeholder:n("edit.form.subscription_none")})}),e.jsxs(Y,{children:[e.jsx(q,{value:"0",children:n("edit.form.commission_type_system")}),e.jsx(q,{value:"1",children:n("edit.form.commission_type_cycle")}),e.jsx(q,{value:"2",children:n("edit.form.commission_type_onetime")})]})]})})]})}),e.jsx(b,{control:a.control,name:"commission_rate",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.commission_rate")}),e.jsx(N,{children:e.jsxs("div",{className:"flex",children:[e.jsx(T,{type:"number",value:i.value||"",onChange:d=>i.onChange(parseInt(d.currentTarget.value)||null),placeholder:n("edit.form.commission_rate_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"%"})]})})]})}),e.jsx(b,{control:a.control,name:"discount",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.discount")}),e.jsx(N,{children:e.jsxs("div",{className:"flex",children:[e.jsx(T,{type:"number",value:i.value||"",onChange:d=>i.onChange(parseInt(d.currentTarget.value)||null),placeholder:n("edit.form.discount_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"%"})]})}),e.jsx(k,{})]})}),e.jsx(b,{control:a.control,name:"speed_limit",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.speed_limit")}),e.jsx(N,{children:e.jsxs("div",{className:"flex",children:[e.jsx(T,{type:"number",value:i.value||"",onChange:d=>i.onChange(parseInt(d.currentTarget.value)||null),placeholder:n("edit.form.speed_limit_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"Mbps"})]})}),e.jsx(k,{})]})}),e.jsx(b,{control:a.control,name:"device_limit",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.device_limit")}),e.jsx(N,{children:e.jsxs("div",{className:"flex",children:[e.jsx(T,{type:"number",value:i.value||"",onChange:d=>i.onChange(parseInt(d.currentTarget.value)||null),placeholder:n("edit.form.device_limit_placeholder"),className:"rounded-r-none"}),e.jsx("div",{className:"z-[-1] rounded-md rounded-l-none border border-l-0 border-input px-3 py-1 shadow-sm",children:"台"})]})}),e.jsx(k,{})]})}),e.jsx(b,{control:a.control,name:"is_admin",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.is_admin")}),e.jsx("div",{className:"py-2",children:e.jsx(N,{children:e.jsx(Q,{checked:i.value===1,onCheckedChange:d=>i.onChange(d?1:0)})})})]})}),e.jsx(b,{control:a.control,name:"is_staff",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.is_staff")}),e.jsx("div",{className:"py-2",children:e.jsx(N,{children:e.jsx(Q,{checked:i.value===1,onCheckedChange:d=>i.onChange(d?1:0)})})})]})}),e.jsx(b,{control:a.control,name:"remarks",render:({field:i})=>e.jsxs(v,{children:[e.jsx(y,{children:n("edit.form.remarks")}),e.jsx(N,{children:e.jsx(bs,{className:"h-24",value:i.value||"",onChange:d=>i.onChange(d.currentTarget.value??null),placeholder:n("edit.form.remarks_placeholder")})}),e.jsx(k,{})]})}),e.jsxs(cl,{children:[e.jsx(D,{variant:"outline",onClick:()=>l(!1),children:n("edit.form.cancel")}),e.jsx(D,{type:"submit",onClick:()=>{a.handleSubmit(i=>{ud(i).then(({data:d})=>{d&&(A.success(n("edit.form.success")),l(!1),s())})})()},children:n("edit.form.submit")})]})]})}function ul({refetch:s,defaultValues:n,dialogTrigger:a=e.jsxs(D,{variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex",children:[e.jsx(vt,{className:"mr-2 h-4 w-4"}),t("edit.button")]})}){const{t:r}=M("user"),[l,c]=m.useState(!1);return e.jsx(_h,{defaultValues:n,open:l,onOpenChange:c,children:e.jsxs(ll,{open:l,onOpenChange:c,children:[e.jsx(il,{asChild:!0,children:a}),e.jsxs($a,{className:"max-w-[90%] space-y-4",children:[e.jsxs(qa,{children:[e.jsx(Ha,{children:r("edit.title")}),e.jsx(Ua,{})]}),e.jsx(Ch,{refetch:s})]})]})})}const ml=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"m17.71 11.29l-5-5a1 1 0 0 0-.33-.21a1 1 0 0 0-.76 0a1 1 0 0 0-.33.21l-5 5a1 1 0 0 0 1.42 1.42L11 9.41V17a1 1 0 0 0 2 0V9.41l3.29 3.3a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.42"})}),xl=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M17.71 11.29a1 1 0 0 0-1.42 0L13 14.59V7a1 1 0 0 0-2 0v7.59l-3.29-3.3a1 1 0 0 0-1.42 1.42l5 5a1 1 0 0 0 .33.21a.94.94 0 0 0 .76 0a1 1 0 0 0 .33-.21l5-5a1 1 0 0 0 0-1.42"})}),Sh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M17 11H9.41l3.3-3.29a1 1 0 1 0-1.42-1.42l-5 5a1 1 0 0 0-.21.33a1 1 0 0 0 0 .76a1 1 0 0 0 .21.33l5 5a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.42L9.41 13H17a1 1 0 0 0 0-2"})}),kh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M17.92 11.62a1 1 0 0 0-.21-.33l-5-5a1 1 0 0 0-1.42 1.42l3.3 3.29H7a1 1 0 0 0 0 2h7.59l-3.3 3.29a1 1 0 0 0 0 1.42a1 1 0 0 0 1.42 0l5-5a1 1 0 0 0 .21-.33a1 1 0 0 0 0-.76"})}),ha=[{accessorKey:"record_at",header:"时间",cell:({row:s})=>e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx("time",{className:"text-sm text-muted-foreground",children:mc(s.original.record_at)})})},{accessorKey:"u",header:"上行流量",cell:({row:s})=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ml,{className:"h-4 w-4 text-emerald-500"}),e.jsx("span",{className:"font-mono text-sm",children:hs(s.original.u)})]})},{accessorKey:"d",header:"下行流量",cell:({row:s})=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(xl,{className:"h-4 w-4 text-blue-500"}),e.jsx("span",{className:"font-mono text-sm",children:hs(s.original.d)})]})},{accessorKey:"server_rate",header:"倍率",cell:({row:s})=>{const n=s.original.server_rate;return e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs(K,{variant:"outline",className:"font-mono",children:[n,"x"]})})}},{id:"total",header:"总计",cell:({row:s})=>{const n=s.original.u+s.original.d;return e.jsx("div",{className:"flex items-center justify-end font-mono text-sm",children:hs(n)})}}];function hl({user_id:s,dialogTrigger:n}){const{t:a}=M(["traffic"]),[r,l]=m.useState(!1),[c,o]=m.useState({pageIndex:0,pageSize:20}),{data:u,isLoading:x}=ie({queryKey:["userStats",s,c,r],queryFn:()=>r?hd({user_id:s,pageSize:c.pageSize,page:c.pageIndex+1}):null}),i=ts({data:u?.data??[],columns:ha,pageCount:Math.ceil((u?.total??0)/c.pageSize),state:{pagination:c},manualPagination:!0,getCoreRowModel:as(),onPaginationChange:o});return e.jsxs(ve,{open:r,onOpenChange:l,children:[e.jsx(Ye,{asChild:!0,children:n}),e.jsxs(pe,{className:"sm:max-w-[700px]",children:[e.jsx(we,{children:e.jsx(be,{children:a("trafficRecord.title")})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"rounded-md border",children:e.jsxs(Va,{children:[e.jsx(Ma,{children:i.getHeaderGroups().map(d=>e.jsx(Rs,{children:d.headers.map(f=>e.jsx(Oa,{className:_("h-10 px-2 text-xs",f.id==="total"&&"text-right"),children:f.isPlaceholder?null:Vt(f.column.columnDef.header,f.getContext())},f.id))},d.id))}),e.jsx(Fa,{children:x?Array.from({length:c.pageSize}).map((d,f)=>e.jsx(Rs,{children:Array.from({length:ha.length}).map((w,P)=>e.jsx(Js,{className:"p-2",children:e.jsx(oe,{className:"h-6 w-full"})},P))},f)):i.getRowModel().rows?.length?i.getRowModel().rows.map(d=>e.jsx(Rs,{"data-state":d.getIsSelected()&&"selected",className:"h-10",children:d.getVisibleCells().map(f=>e.jsx(Js,{className:"px-2",children:Vt(f.column.columnDef.cell,f.getContext())},f.id))},d.id)):e.jsx(Rs,{children:e.jsx(Js,{colSpan:ha.length,className:"h-24 text-center",children:a("trafficRecord.noRecords")})})})]})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("p",{className:"text-sm font-medium",children:a("trafficRecord.perPage")}),e.jsxs(J,{value:`${i.getState().pagination.pageSize}`,onValueChange:d=>{i.setPageSize(Number(d))},children:[e.jsx(W,{className:"h-8 w-[70px]",children:e.jsx(Z,{placeholder:i.getState().pagination.pageSize})}),e.jsx(Y,{side:"top",children:[10,20,30,40,50].map(d=>e.jsx(q,{value:`${d}`,children:d},d))})]}),e.jsx("p",{className:"text-sm font-medium",children:a("trafficRecord.records")})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex w-[100px] items-center justify-center text-sm",children:a("trafficRecord.page",{current:i.getState().pagination.pageIndex+1,total:i.getPageCount()})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(X,{variant:"outline",size:"icon",className:"h-8 w-8",onClick:()=>i.previousPage(),disabled:!i.getCanPreviousPage()||x,children:e.jsx(Sh,{className:"h-4 w-4"})}),e.jsx(X,{variant:"outline",size:"icon",className:"h-8 w-8",onClick:()=>i.nextPage(),disabled:!i.getCanNextPage()||x,children:e.jsx(kh,{className:"h-4 w-4"})})]})]})]})]})]})]})}function Th({onConfirm:s,children:n,title:a="确认操作",description:r="确定要执行此操作吗？",cancelText:l="取消",confirmText:c="确认",variant:o="default",className:u}){return e.jsxs(za,{children:[e.jsx(Kr,{asChild:!0,children:n}),e.jsxs(Qt,{className:_("sm:max-w-[425px]",u),children:[e.jsxs(Jt,{children:[e.jsx(Xt,{children:a}),e.jsx(ea,{children:r})]}),e.jsxs(Zt,{children:[e.jsx(ta,{asChild:!0,children:e.jsx(D,{variant:"outline",children:l})}),e.jsx(sa,{asChild:!0,children:e.jsx(D,{variant:o,onClick:s,children:c})})]})]})]})}const Dh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M5 18h4.24a1 1 0 0 0 .71-.29l6.92-6.93L19.71 8a1 1 0 0 0 0-1.42l-4.24-4.29a1 1 0 0 0-1.42 0l-2.82 2.83l-6.94 6.93a1 1 0 0 0-.29.71V17a1 1 0 0 0 1 1m9.76-13.59l2.83 2.83l-1.42 1.42l-2.83-2.83ZM6 13.17l5.93-5.93l2.83 2.83L8.83 16H6ZM21 20H3a1 1 0 0 0 0 2h18a1 1 0 0 0 0-2"})}),Ph=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2"})}),Eh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M21 8.94a1.3 1.3 0 0 0-.06-.27v-.09a1 1 0 0 0-.19-.28l-6-6a1 1 0 0 0-.28-.19a.3.3 0 0 0-.09 0a.9.9 0 0 0-.33-.11H10a3 3 0 0 0-3 3v1H6a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3v-1h1a3 3 0 0 0 3-3zm-6-3.53L17.59 8H16a1 1 0 0 1-1-1ZM15 19a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h1v7a3 3 0 0 0 3 3h5Zm4-4a1 1 0 0 1-1 1h-8a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h3v3a3 3 0 0 0 3 3h3Z"})}),Rh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M21 11a1 1 0 0 0-1 1a8.05 8.05 0 1 1-2.22-5.5h-2.4a1 1 0 0 0 0 2h4.53a1 1 0 0 0 1-1V3a1 1 0 0 0-2 0v1.77A10 10 0 1 0 22 12a1 1 0 0 0-1-1"})}),Ih=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M9.5 10.5H12a1 1 0 0 0 0-2h-1V8a1 1 0 0 0-2 0v.55a2.5 2.5 0 0 0 .5 4.95h1a.5.5 0 0 1 0 1H8a1 1 0 0 0 0 2h1v.5a1 1 0 0 0 2 0v-.55a2.5 2.5 0 0 0-.5-4.95h-1a.5.5 0 0 1 0-1M21 12h-3V3a1 1 0 0 0-.5-.87a1 1 0 0 0-1 0l-3 1.72l-3-1.72a1 1 0 0 0-1 0l-3 1.72l-3-1.72a1 1 0 0 0-1 0A1 1 0 0 0 2 3v16a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3v-6a1 1 0 0 0-1-1M5 20a1 1 0 0 1-1-1V4.73l2 1.14a1.08 1.08 0 0 0 1 0l3-1.72l3 1.72a1.08 1.08 0 0 0 1 0l2-1.14V19a3 3 0 0 0 .18 1Zm15-1a1 1 0 0 1-2 0v-5h2Z"})}),Vh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M12.3 12.22A4.92 4.92 0 0 0 14 8.5a5 5 0 0 0-10 0a4.92 4.92 0 0 0 1.7 3.72A8 8 0 0 0 1 19.5a1 1 0 0 0 2 0a6 6 0 0 1 12 0a1 1 0 0 0 2 0a8 8 0 0 0-4.7-7.28M9 11.5a3 3 0 1 1 3-3a3 3 0 0 1-3 3m9.74.32A5 5 0 0 0 15 3.5a1 1 0 0 0 0 2a3 3 0 0 1 3 3a3 3 0 0 1-1.5 2.59a1 1 0 0 0-.5.84a1 1 0 0 0 .45.86l.39.26l.13.07a7 7 0 0 1 4 6.38a1 1 0 0 0 2 0a9 9 0 0 0-4.23-7.68"})}),Mh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M12 2a10 10 0 0 0-6.88 2.77V3a1 1 0 0 0-2 0v4.5a1 1 0 0 0 1 1h4.5a1 1 0 0 0 0-2h-2.4A8 8 0 1 1 4 12a1 1 0 0 0-2 0A10 10 0 1 0 12 2m0 6a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h2a1 1 0 0 0 0-2h-1V9a1 1 0 0 0-1-1"})}),Fh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M20 6h-4V5a3 3 0 0 0-3-3h-2a3 3 0 0 0-3 3v1H4a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V8h1a1 1 0 0 0 0-2M10 5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v1h-4Zm7 14a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1V8h10Z"})}),Oh=(s,n)=>{const{t:a}=M("user");return[{accessorKey:"is_admin",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.is_admin")}),enableSorting:!1,enableHiding:!0,filterFn:(r,l,c)=>c.includes(r.getValue(l)),size:0},{accessorKey:"is_staff",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.is_staff")}),enableSorting:!1,enableHiding:!0,filterFn:(r,l,c)=>c.includes(r.getValue(l)),size:0},{accessorKey:"id",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.id")}),cell:({row:r})=>e.jsx(K,{variant:"outline",children:r.original.id}),enableSorting:!0,enableHiding:!1},{accessorKey:"email",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.email")}),cell:({row:r})=>{const l=r.original.t||0,c=Date.now()/1e3-l<120,o=Math.floor(Date.now()/1e3-l);let u=c?a("columns.online_status.online"):l===0?a("columns.online_status.never"):a("columns.online_status.last_online",{time:_e(l)});if(!c&&l!==0){const x=Math.floor(o/60),i=Math.floor(x/60),d=Math.floor(i/24);d>0?u+=`
`+a("columns.online_status.offline_duration.days",{count:d}):i>0?u+=`
`+a("columns.online_status.offline_duration.hours",{count:i}):x>0?u+=`
`+a("columns.online_status.offline_duration.minutes",{count:x}):u+=`
`+a("columns.online_status.offline_duration.seconds",{count:o})}return e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{children:e.jsxs("div",{className:"flex items-center gap-2.5",children:[e.jsx("div",{className:_("size-2.5 rounded-full ring-2 ring-offset-2",c?"bg-green-500 ring-green-500/20":"bg-gray-300 ring-gray-300/20","transition-all duration-300")}),e.jsx("span",{className:"font-medium text-foreground/90",children:r.original.email})]})}),e.jsx(ce,{side:"bottom",className:"max-w-[280px]",children:e.jsx("p",{className:"whitespace-pre-line text-sm",children:u})})]})})},enableSorting:!1,enableHiding:!1},{accessorKey:"online_count",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.online_count")}),cell:({row:r})=>{const l=r.original.device_limit,c=r.original.online_count||0;return e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{children:e.jsx("div",{className:"flex items-center gap-1.5",children:e.jsxs(K,{variant:"outline",className:_("min-w-[4rem] justify-center",l!==null&&c>=l?"border-destructive/50 bg-destructive/10 text-destructive":"border-primary/40 bg-primary/5 text-primary/90"),children:[c," / ",l===null?"∞":l]})})}),e.jsx(ce,{side:"bottom",children:e.jsx("p",{className:"text-sm",children:l===null?a("columns.device_limit.unlimited"):a("columns.device_limit.limited",{count:l})})})]})})},enableSorting:!0,enableHiding:!1},{accessorKey:"banned",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.status")}),cell:({row:r})=>{const l=r.original.banned;return e.jsx("div",{className:"flex justify-center",children:e.jsx(K,{className:_("min-w-20 justify-center transition-colors",l?"bg-destructive/15 text-destructive hover:bg-destructive/25":"bg-success/15 text-success hover:bg-success/25"),children:a(l?"columns.status_text.banned":"columns.status_text.normal")})})},enableSorting:!0,filterFn:(r,l,c)=>c.includes(r.getValue(l))},{accessorKey:"plan_id",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.subscription")}),cell:({row:r})=>e.jsx("div",{className:"min-w-[10em] break-all",children:r.original?.plan?.name||"-"}),enableSorting:!1,enableHiding:!1},{accessorKey:"group_id",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.group")}),cell:({row:r})=>e.jsx("div",{className:"flex flex-wrap gap-1",children:e.jsx(K,{variant:"outline",className:_("px-2 py-0.5 font-medium","bg-secondary/50 hover:bg-secondary/70","border border-border/50","transition-all duration-200","cursor-default select-none","flex items-center gap-1.5 whitespace-nowrap"),children:r.original?.group?.name||"-"})}),enableSorting:!1},{accessorKey:"total_used",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.used_traffic")}),cell:({row:r})=>{const l=hs(r.original?.total_used),c=hs(r.original?.transfer_enable),o=r.original?.total_used/r.original?.transfer_enable*100||0;return e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{className:"w-full",children:e.jsxs("div",{className:"w-full space-y-1",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-muted-foreground",children:l}),e.jsxs("span",{className:"text-xs text-muted-foreground",children:[o.toFixed(1),"%"]})]}),e.jsx("div",{className:"h-1.5 w-full rounded-full bg-secondary",children:e.jsx("div",{className:_("h-full rounded-full transition-all",o>90?"bg-destructive":"bg-primary"),style:{width:`${Math.min(o,100)}%`}})})]})}),e.jsx(ce,{side:"bottom",children:e.jsxs("p",{className:"text-sm",children:[a("columns.total_traffic"),": ",c]})})]})})}},{accessorKey:"transfer_enable",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.total_traffic")}),cell:({row:r})=>e.jsx("div",{className:"font-medium text-muted-foreground",children:hs(r.original?.transfer_enable)})},{accessorKey:"expired_at",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.expire_time")}),cell:({row:r})=>{const l=r.original.expired_at,c=Date.now()/1e3,o=l!=null&&l<c,u=l!=null?new Date(l*1e3):null,x=u!=null?Math.ceil((u.getTime()-Date.now())/(1e3*60*60*24)):null;return e.jsx(je,{delayDuration:100,children:e.jsxs(xe,{children:[e.jsx(he,{className:"block w-full",children:e.jsx(K,{variant:"outline",className:_("w-full justify-center transition-colors",o?"border-destructive/50 bg-destructive/10 text-destructive":l?"border-success/50 bg-success/10 text-success":"border-primary/40 bg-primary/5 text-primary/90"),children:l==null?a("columns.expire_status.permanent"):_e(l,"YYYY-MM-DD")})}),e.jsx(ce,{side:"bottom",className:"space-y-1 p-3",children:l!=null?e.jsxs(e.Fragment,{children:[e.jsxs("p",{className:"text-sm text-muted-foreground",children:[a("columns.expire_time"),": ",_e(l)]}),e.jsx("p",{className:_("text-sm font-medium",o?"text-destructive":"text-success"),children:o?a("columns.expire_status.expired",{days:Math.abs(x??0)}):a("columns.expire_status.remaining",{days:x??0})})]}):e.jsx("p",{className:"text-sm font-medium",children:a("columns.expire_status.permanent")})})]})})}},{accessorKey:"balance",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.balance")}),cell:({row:r})=>{const l=Qs(r.original?.balance);return e.jsxs("div",{className:"flex items-center gap-1 font-medium",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"¥"}),e.jsx("span",{className:"tabular-nums text-foreground",children:l})]})}},{accessorKey:"commission_balance",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.commission")}),cell:({row:r})=>{const l=Qs(r.original?.commission_balance);return e.jsxs("div",{className:"flex items-center gap-1 font-medium",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"¥"}),e.jsx("span",{className:"tabular-nums text-foreground",children:l})]})}},{accessorKey:"created_at",header:({column:r})=>e.jsx(L,{column:r,title:a("columns.register_time")}),cell:({row:r})=>e.jsx("div",{className:"truncate",children:_e(r.original?.created_at)}),size:1e3},{id:"actions",header:({column:r})=>e.jsx(L,{column:r,className:"justify-end",title:a("columns.actions")}),cell:({row:r,table:l})=>e.jsxs(Cs,{modal:!0,children:[e.jsx(Ss,{asChild:!0,children:e.jsx("div",{className:"text-center",children:e.jsx(X,{variant:"ghost",className:"h-8 w-8 p-0 hover:bg-muted","aria-label":a("columns.actions"),children:e.jsx(Ft,{className:"size-4"})})})}),e.jsxs(ps,{align:"end",className:"min-w-[40px]",children:[e.jsx(ge,{onSelect:c=>{c.preventDefault()},className:"p-0",children:e.jsx(ul,{defaultValues:{...r.original,invite_user_email:r.original.invite_user?.email},refetch:s,dialogTrigger:e.jsxs(X,{variant:"ghost",className:"w-full justify-start px-2 py-1.5",children:[e.jsx(Dh,{className:"mr-2"}),a("columns.actions_menu.edit")]})})}),e.jsx(ge,{onSelect:c=>c.preventDefault(),className:"p-0",children:e.jsx(tl,{defaultValues:{email:r.original.email},trigger:e.jsxs(X,{variant:"ghost",className:"w-full justify-start px-2 py-1.5",children:[e.jsx(Ph,{className:"mr-2 "}),a("columns.actions_menu.assign_order")]})})}),e.jsx(ge,{onSelect:()=>{zt(r.original.subscribe_url).then(()=>{A.success(a("common:copy.success"))})},className:"p-0",children:e.jsxs(X,{variant:"ghost",className:"w-full justify-start px-2 py-1.5",children:[e.jsx(Eh,{className:"mr-2"}),a("columns.actions_menu.copy_url")]})}),e.jsx(ge,{onSelect:()=>{md({id:r.original.id}).then(({data:c})=>{c&&A.success("重置成功")})},children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Rh,{className:"mr-2 "}),a("columns.actions_menu.reset_secret")]})}),e.jsx(ge,{onSelect:()=>{},className:"p-0",children:e.jsxs($s,{className:"flex items-center px-2 py-1.5",to:`/finance/order?user_id=${r.original?.id}`,children:[e.jsx(Ih,{className:"mr-2"}),a("columns.actions_menu.orders")]})}),e.jsx(ge,{onSelect:()=>{l.setColumnFilters([{id:"invite_user_id",value:r.original?.id}])},children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Vh,{className:"mr-2 "}),a("columns.actions_menu.invites")]})}),e.jsx(ge,{onSelect:c=>c.preventDefault(),className:"p-0",children:e.jsx(hl,{user_id:r.original?.id,dialogTrigger:e.jsxs(X,{variant:"ghost",className:"w-full justify-start px-2 py-1.5",children:[e.jsx(Mh,{className:"mr-2 "}),a("columns.actions_menu.traffic_records")]})})}),e.jsx(ge,{onSelect:c=>c.preventDefault(),className:"p-0",children:e.jsx(Th,{title:a("columns.actions_menu.delete_confirm_title"),description:a("columns.actions_menu.delete_confirm_description",{email:r.original.email}),cancelText:a("common:cancel"),confirmText:a("common:confirm"),variant:"destructive",onConfirm:async()=>{try{const{data:c}=await Lt.destroy(r.original.id);c&&(A.success(a("common:delete.success")),s())}catch{A.error(a("common:delete.failed"))}},children:e.jsxs(X,{variant:"ghost",className:"w-full justify-start px-2 py-1.5 text-destructive hover:text-destructive",children:[e.jsx(Fh,{className:"mr-2"}),a("columns.actions_menu.delete")]})})})]})]})}]};function zh(){const[s]=fr(),[n,a]=m.useState({}),[r,l]=m.useState({is_admin:!1,is_staff:!1}),[c,o]=m.useState([]),[u,x]=m.useState([]),[i,d]=m.useState({pageIndex:0,pageSize:20});m.useEffect(()=>{const F=s.get("email");F&&o(H=>H.some(O=>O.id==="email")?H:[...H,{id:"email",value:F}])},[s]);const{refetch:f,data:w,isLoading:P}=ie({queryKey:["userList",i,c,u],queryFn:()=>dd({pageSize:i.pageSize,current:i.pageIndex+1,filter:c,sort:u})}),[C,p]=m.useState([]),[g,j]=m.useState([]);m.useEffect(()=>{yt().then(({data:F})=>{p(F)}),Hs().then(({data:F})=>{j(F)})},[]);const S=C.map(F=>({label:F.name,value:F.id})),R=g.map(F=>({label:F.name,value:F.id})),E=ts({data:w?.data??[],columns:Oh(f),state:{sorting:u,columnVisibility:r,rowSelection:n,columnFilters:c,pagination:i},rowCount:w?.total??0,manualPagination:!0,manualFiltering:!0,manualSorting:!0,enableRowSelection:!0,onRowSelectionChange:a,onSortingChange:x,onColumnFiltersChange:o,onColumnVisibilityChange:l,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),onPaginationChange:d,getSortedRowModel:us(),getFacetedRowModel:Ts(),getFacetedUniqueValues:Ds(),initialState:{columnVisibility:{commission_balance:!1,created_at:!1,is_admin:!1,is_staff:!1,permission_group:!1,plan_id:!1},columnPinning:{right:["actions"]}}});return e.jsx(xs,{table:E,toolbar:e.jsx(yh,{table:E,refetch:f,serverGroupList:C,permissionGroups:S,subscriptionPlans:R})})}function Lh(){const{t:s}=M("user");return e.jsxs(Pe,{children:[e.jsxs(Ee,{children:[e.jsx(qe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("manage.title")}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:s("manage.description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx("div",{className:"w-full",children:e.jsx(zh,{})})})]})]})}const Ah=Object.freeze(Object.defineProperty({__proto__:null,default:Lh},Symbol.toStringTag,{value:"Module"}));function $h({column:s,title:n,options:a}){const r=new Set(s?.getFilterValue());return e.jsxs(js,{children:[e.jsx(vs,{asChild:!0,children:e.jsxs(X,{variant:"outline",size:"sm",className:"h-8 border-dashed",children:[e.jsx(Yo,{className:"mr-2 h-4 w-4"}),n,r?.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(Se,{orientation:"vertical",className:"mx-2 h-4"}),e.jsx(K,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:r.size}),e.jsx("div",{className:"hidden space-x-1 lg:flex",children:r.size>2?e.jsxs(K,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[r.size," selected"]}):a.filter(l=>r.has(l.value)).map(l=>e.jsx(K,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:l.label},`selected-${l.value}`))})]})]})}),e.jsx(ms,{className:"w-[200px] p-0",align:"start",children:e.jsxs(Vs,{children:[e.jsx(Us,{placeholder:n}),e.jsxs(Ms,{children:[e.jsx(Ks,{children:"No results found."}),e.jsx(Ge,{children:a.map(l=>{const c=r.has(l.value);return e.jsxs(Ie,{onSelect:()=>{c?r.delete(l.value):r.add(l.value);const o=Array.from(r);s?.setFilterValue(o.length?o:void 0)},children:[e.jsx("div",{className:_("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",c?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(Qo,{className:_("h-4 w-4")})}),l.icon&&e.jsx(l.icon,{className:"mr-2 h-4 w-4 text-muted-foreground"}),e.jsx("span",{children:l.label})]},`option-${l.value}`)})}),r.size>0&&e.jsxs(e.Fragment,{children:[e.jsx(at,{}),e.jsx(Ge,{children:e.jsx(Ie,{onSelect:()=>s?.setFilterValue(void 0),className:"justify-center text-center",children:"Clear filters"})})]})]})]})})]})}const qh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M19 11H5a1 1 0 0 0 0 2h14a1 1 0 0 0 0-2"})});function Hh({table:s}){const{t:n}=M("ticket");return e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-4",children:[e.jsx(Wt,{defaultValue:s.getColumn("status")?.getFilterValue(),onValueChange:a=>s.getColumn("status")?.setFilterValue(a),children:e.jsxs(bt,{className:"grid w-full grid-cols-2",children:[e.jsx(Ke,{value:"0",children:n("status.pending")}),e.jsx(Ke,{value:"1",children:n("status.closed")})]})}),s.getColumn("level")&&e.jsx($h,{column:s.getColumn("level"),title:n("columns.level"),options:[{label:n("level.low"),value:Me.LOW,icon:qh,color:"gray"},{label:n("level.medium"),value:Me.MIDDLE,icon:ml,color:"yellow"},{label:n("level.high"),value:Me.HIGH,icon:xl,color:"red"}]})]})})}function Uh(){return e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"text-foreground",children:[e.jsx("circle",{cx:"4",cy:"12",r:"2",fill:"currentColor",children:e.jsx("animate",{id:"spinner_qFRN",begin:"0;spinner_OcgL.end+0.25s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),e.jsx("circle",{cx:"12",cy:"12",r:"2",fill:"currentColor",children:e.jsx("animate",{begin:"spinner_qFRN.begin+0.1s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),e.jsx("circle",{cx:"20",cy:"12",r:"2",fill:"currentColor",children:e.jsx("animate",{id:"spinner_OcgL",begin:"spinner_qFRN.begin+0.2s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})})]})}const Kh=Ls("flex gap-2 max-w-[60%] items-end relative group",{variants:{variant:{received:"self-start",sent:"self-end flex-row-reverse"},layout:{default:"",ai:"max-w-full w-full items-center"}},defaultVariants:{variant:"received",layout:"default"}}),pl=m.forwardRef(({className:s,variant:n,layout:a,children:r,...l},c)=>e.jsx("div",{className:_(Kh({variant:n,layout:a,className:s}),"relative group"),ref:c,...l,children:m.Children.map(r,o=>m.isValidElement(o)&&typeof o.type!="string"?m.cloneElement(o,{variant:n,layout:a}):o)}));pl.displayName="ChatBubble";const Bh=Ls("p-4",{variants:{variant:{received:"bg-secondary text-secondary-foreground rounded-r-lg rounded-tl-lg",sent:"bg-primary text-primary-foreground rounded-l-lg rounded-tr-lg"},layout:{default:"",ai:"border-t w-full rounded-none bg-transparent"}},defaultVariants:{variant:"received",layout:"default"}}),fl=m.forwardRef(({className:s,variant:n,layout:a,isLoading:r=!1,children:l,...c},o)=>e.jsx("div",{className:_(Bh({variant:n,layout:a,className:s}),"break-words max-w-full whitespace-pre-wrap"),ref:o,...c,children:r?e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(Uh,{})}):l}));fl.displayName="ChatBubbleMessage";const Gh=m.forwardRef(({variant:s,className:n,children:a,...r},l)=>e.jsx("div",{ref:l,className:_("absolute top-1/2 -translate-y-1/2 flex opacity-0 group-hover:opacity-100 transition-opacity duration-200",s==="sent"?"-left-1 -translate-x-full flex-row-reverse":"-right-1 translate-x-full",n),...r,children:a}));Gh.displayName="ChatBubbleActionWrapper";const gl=m.forwardRef(({className:s,...n},a)=>e.jsx(bs,{autoComplete:"off",ref:a,name:"message",className:_("max-h-12 px-4 py-3 bg-background text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 w-full rounded-md flex items-center h-16 resize-none",s),...n}));gl.displayName="ChatInput";const jl=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"m13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29l-4.3 4.29a1 1 0 0 0 0 1.42a1 1 0 0 0 1.42 0l4.29-4.3l4.29 4.3a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.42Z"})}),vl=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M15.098 12.634L13 11.423V7a1 1 0 0 0-2 0v5a1 1 0 0 0 .5.866l2.598 1.5a1 1 0 1 0 1-1.732M12 2a10 10 0 1 0 10 10A10.01 10.01 0 0 0 12 2m0 18a8 8 0 1 1 8-8a8.01 8.01 0 0 1-8 8"})}),pn=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"m11.29 12l3.54-3.54a1 1 0 0 0 0-1.41a1 1 0 0 0-1.42 0l-4.24 4.24a1 1 0 0 0 0 1.42L13.41 17a1 1 0 0 0 .71.29a1 1 0 0 0 .71-.29a1 1 0 0 0 0-1.41Z"})}),Wh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M21.71 20.29L18 16.61A9 9 0 1 0 16.61 18l3.68 3.68a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.39M11 18a7 7 0 1 1 7-7a7 7 0 0 1-7 7"})}),Yh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M3.71 16.29a1 1 0 0 0-.33-.21a1 1 0 0 0-.76 0a1 1 0 0 0-.33.21a1 1 0 0 0-.21.33a1 1 0 0 0 .21 1.09a1.2 1.2 0 0 0 .33.21a.94.94 0 0 0 .76 0a1.2 1.2 0 0 0 .33-.21a1 1 0 0 0 .21-1.09a1 1 0 0 0-.21-.33M7 8h14a1 1 0 0 0 0-2H7a1 1 0 0 0 0 2m-3.29 3.29a1 1 0 0 0-1.09-.21a1.2 1.2 0 0 0-.33.21a1 1 0 0 0-.21.33a.94.94 0 0 0 0 .76a1.2 1.2 0 0 0 .21.33a1.2 1.2 0 0 0 .33.21a.94.94 0 0 0 .76 0a1.2 1.2 0 0 0 .33-.21a1.2 1.2 0 0 0 .21-.33a.94.94 0 0 0 0-.76a1 1 0 0 0-.21-.33M21 11H7a1 1 0 0 0 0 2h14a1 1 0 0 0 0-2M3.71 6.29a1 1 0 0 0-.33-.21a1 1 0 0 0-1.09.21a1.2 1.2 0 0 0-.21.33a.94.94 0 0 0 0 .76a1.2 1.2 0 0 0 .21.33a1.2 1.2 0 0 0 .33.21a1 1 0 0 0 1.09-.21a1.2 1.2 0 0 0 .21-.33a.94.94 0 0 0 0-.76a1.2 1.2 0 0 0-.21-.33M21 16H7a1 1 0 0 0 0 2h14a1 1 0 0 0 0-2"})}),Qh=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M9 12H7a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2m-1-2h4a1 1 0 0 0 0-2H8a1 1 0 0 0 0 2m1 6H7a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2m12-4h-3V3a1 1 0 0 0-.5-.87a1 1 0 0 0-1 0l-3 1.72l-3-1.72a1 1 0 0 0-1 0l-3 1.72l-3-1.72a1 1 0 0 0-1 0A1 1 0 0 0 2 3v16a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3v-6a1 1 0 0 0-1-1M5 20a1 1 0 0 1-1-1V4.73l2 1.14a1.08 1.08 0 0 0 1 0l3-1.72l3 1.72a1.08 1.08 0 0 0 1 0l2-1.14V19a3 3 0 0 0 .18 1Zm15-1a1 1 0 0 1-2 0v-5h2Zm-6.44-2.83a.8.8 0 0 0-.18-.09a.6.6 0 0 0-.19-.06a1 1 0 0 0-.9.27A1.05 1.05 0 0 0 12 17a1 1 0 0 0 .07.38a1.2 1.2 0 0 0 .22.33a1.2 1.2 0 0 0 .33.21a.94.94 0 0 0 .76 0a1.2 1.2 0 0 0 .33-.21A1 1 0 0 0 14 17a1.05 1.05 0 0 0-.29-.71a2 2 0 0 0-.15-.12m.14-3.88a1 1 0 0 0-1.62.33A1 1 0 0 0 13 14a1 1 0 0 0 1-1a1 1 0 0 0-.08-.38a.9.9 0 0 0-.22-.33"})});function Jh(){return e.jsxs("div",{className:"flex h-full flex-col space-y-4 p-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(oe,{className:"h-8 w-3/4"}),e.jsx(oe,{className:"h-4 w-1/2"})]}),e.jsx("div",{className:"flex-1 space-y-4",children:[1,2,3].map(s=>e.jsx(oe,{className:"h-20 w-2/3"},s))})]})}function Zh(){return e.jsx("div",{className:"space-y-4 p-4",children:[1,2,3,4].map(s=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(oe,{className:"h-5 w-4/5"}),e.jsx(oe,{className:"h-4 w-2/3"}),e.jsx(oe,{className:"h-3 w-1/2"})]},s))})}function Xh({ticket:s,isActive:n,onClick:a}){const{t:r}=M("ticket"),l=c=>{switch(c){case Me.HIGH:return"bg-red-50 text-red-600 border-red-200";case Me.MIDDLE:return"bg-yellow-50 text-yellow-600 border-yellow-200";case Me.LOW:return"bg-green-50 text-green-600 border-green-200";default:return"bg-gray-50 text-gray-600 border-gray-200"}};return e.jsxs("div",{className:_("flex cursor-pointer flex-col border-b p-4 hover:bg-accent/50",n&&"bg-accent"),onClick:a,children:[e.jsxs("div",{className:"flex items-center justify-between gap-2 max-w-[280px]",children:[e.jsx("h4",{className:"truncate font-medium flex-1",children:s.subject}),e.jsx(K,{variant:s.status===Is.CLOSED?"secondary":"default",className:"shrink-0",children:s.status===Is.CLOSED?r("status.closed"):r("status.processing")})]}),e.jsx("div",{className:"mt-1 text-sm text-muted-foreground truncate max-w-[280px]",children:s.user?.email}),e.jsxs("div",{className:"mt-2 flex items-center justify-between text-xs",children:[e.jsx("time",{className:"text-muted-foreground",children:_e(s.updated_at)}),e.jsx("div",{className:_("px-2 py-0.5 rounded-full border text-xs font-medium",l(s.level)),children:r(`level.${s.level===Me.LOW?"low":s.level===Me.MIDDLE?"medium":"high"}`)})]})]})}function ep({ticketId:s,dialogTrigger:n}){const{t:a}=M("ticket"),r=ks(),l=m.useRef(null),c=m.useRef(null),[o,u]=m.useState(!1),[x,i]=m.useState(""),[d,f]=m.useState(!1),[w,P]=m.useState(s),[C,p]=m.useState(""),[g,j]=m.useState(!1),{data:S,isLoading:R,refetch:E}=ie({queryKey:["tickets",o],queryFn:()=>o?ia.getList({filter:[{id:"status",value:[Is.OPENING]}]}):Promise.resolve(null),enabled:o}),{data:F,refetch:H,isLoading:te}=ie({queryKey:["ticket",w,o],queryFn:()=>o?fd(w):Promise.resolve(null),refetchInterval:o?5e3:!1,retry:3}),O=F?.data,He=(S?.data||[]).filter(ne=>ne.subject.toLowerCase().includes(C.toLowerCase())||ne.user?.email.toLowerCase().includes(C.toLowerCase())),G=(ne="smooth")=>{if(l.current){const{scrollHeight:Qe,clientHeight:nt}=l.current;l.current.scrollTo({top:Qe-nt,behavior:ne})}};m.useEffect(()=>{if(!o)return;const ne=requestAnimationFrame(()=>{G("instant"),setTimeout(()=>G(),1e3)});return()=>{cancelAnimationFrame(ne)}},[o,O?.messages]);const ae=async()=>{const ne=x.trim();!ne||d||(f(!0),ia.reply({id:w,message:ne}).then(()=>{i(""),H(),G(),setTimeout(()=>{c.current?.focus()},0)}).finally(()=>{f(!1)}))},$=async()=>{ia.close(w).then(()=>{A.success(a("actions.close_success")),H(),E()})},I=()=>{O?.user&&r("/finance/order?user_id="+O.user.id)},B=O?.status===Is.CLOSED;return e.jsxs(ve,{open:o,onOpenChange:u,children:[e.jsx(Ye,{asChild:!0,children:n??e.jsx(X,{variant:"outline",children:a("actions.view_ticket")})}),e.jsxs(pe,{className:"flex h-[90vh] max-w-6xl flex-col gap-0 p-0",children:[e.jsx(be,{}),e.jsxs("div",{className:"flex h-full",children:[e.jsx(X,{variant:"ghost",size:"icon",className:"absolute left-2 top-2 md:hidden z-50",onClick:()=>j(!g),children:e.jsx(pn,{className:_("h-4 w-4 transition-transform",!g&&"rotate-180")})}),e.jsxs("div",{className:_("absolute md:relative inset-y-0 left-0 z-40 flex flex-col border-r bg-background transition-transform duration-200 ease-in-out",g?"-translate-x-full":"translate-x-0","w-80 md:w-80 md:translate-x-0"),children:[e.jsxs("div",{className:"space-y-4 border-b p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"font-semibold",children:a("list.title")}),e.jsx(X,{variant:"ghost",size:"icon",className:"hidden md:flex h-8 w-8",onClick:()=>j(!g),children:e.jsx(pn,{className:_("h-4 w-4 transition-transform",!g&&"rotate-180")})})]}),e.jsxs("div",{className:"relative",children:[e.jsx(Wh,{className:"absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground"}),e.jsx(T,{placeholder:a("list.search_placeholder"),value:C,onChange:ne=>p(ne.target.value),className:"pl-8"})]})]}),e.jsx(zs,{className:"flex-1",children:e.jsx("div",{className:"w-full",children:R?e.jsx(Zh,{}):He.length===0?e.jsx("div",{className:"flex h-full items-center justify-center text-muted-foreground p-4",children:a(C?"list.no_search_results":"list.no_tickets")}):He.map(ne=>e.jsx(Xh,{ticket:ne,isActive:ne.id===w,onClick:()=>{P(ne.id),window.innerWidth<768&&j(!0)}},ne.id))})})]}),e.jsxs("div",{className:"flex-1 flex flex-col relative",children:[!g&&e.jsx("div",{className:"absolute inset-0 bg-black/20 z-30 md:hidden",onClick:()=>j(!0)}),te?e.jsx(Jh,{}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex flex-col space-y-4 border-b p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("h2",{className:"text-2xl font-semibold",children:O?.subject}),e.jsx(K,{variant:B?"secondary":"default",children:a(B?"status.closed":"status.processing")}),!B&&e.jsx(We,{title:a("actions.close_confirm_title"),description:a("actions.close_confirm_description"),confirmText:a("actions.close_confirm_button"),variant:"destructive",onConfirm:$,children:e.jsxs(X,{variant:"ghost",size:"sm",className:"gap-1 text-muted-foreground hover:text-destructive",children:[e.jsx(jl,{className:"h-4 w-4"}),a("actions.close_ticket")]})})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(gt,{className:"h-4 w-4"}),e.jsx("span",{children:O?.user?.email})]}),e.jsx(Se,{orientation:"vertical",className:"h-4"}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(vl,{className:"h-4 w-4"}),e.jsxs("span",{children:[a("detail.created_at")," ",_e(O?.created_at)]})]}),e.jsx(Se,{orientation:"vertical",className:"h-4"}),e.jsx(K,{variant:"outline",children:O?.level!=null&&a(`level.${O.level===Me.LOW?"low":O.level===Me.MIDDLE?"medium":"high"}`)})]})]}),O?.user&&e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(ul,{defaultValues:O.user,refetch:H,dialogTrigger:e.jsx(X,{variant:"outline",size:"icon",className:"h-8 w-8",title:a("detail.user_info"),children:e.jsx(gt,{className:"h-4 w-4"})})}),e.jsx(hl,{user_id:O.user.id,dialogTrigger:e.jsx(X,{variant:"outline",size:"icon",className:"h-8 w-8",title:a("detail.traffic_records"),children:e.jsx(Yh,{className:"h-4 w-4"})})}),e.jsx(X,{variant:"outline",size:"icon",className:"h-8 w-8",title:a("detail.order_records"),onClick:I,children:e.jsx(Qh,{className:"h-4 w-4"})})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:e.jsx("div",{ref:l,className:"h-full space-y-4 overflow-y-auto p-6",children:O?.messages?.length===0?e.jsx("div",{className:"flex h-full items-center justify-center text-muted-foreground",children:a("detail.no_messages")}):O?.messages?.map(ne=>e.jsx(pl,{variant:ne.is_me?"sent":"received",className:ne.is_me?"ml-auto":"mr-auto",children:e.jsx(fl,{children:e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"whitespace-pre-wrap break-words",children:ne.message}),e.jsx("div",{className:"text-right",children:e.jsx("time",{className:"text-[10px] text-muted-foreground",children:_e(ne.created_at)})})]})})},ne.id))})}),e.jsx("div",{className:"border-t p-4",children:e.jsxs("div",{className:"relative flex items-center space-x-2",children:[e.jsx(gl,{ref:c,disabled:B||d,placeholder:a(B?"detail.input.closed_placeholder":"detail.input.reply_placeholder"),className:"flex-1 resize-none rounded-lg border bg-background p-3 focus-visible:ring-1",value:x,onChange:ne=>i(ne.target.value),onKeyDown:ne=>{ne.key==="Enter"&&!ne.shiftKey&&(ne.preventDefault(),ae())}}),e.jsx(X,{disabled:B||d||!x.trim(),onClick:ae,children:a(d?"detail.input.sending":"detail.input.send")})]})})]})]})]})]})]})}const sp=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M19 4H5a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3m-.41 2l-5.88 5.88a1 1 0 0 1-1.42 0L5.41 6ZM20 17a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V7.41l5.88 5.88a3 3 0 0 0 4.24 0L20 7.41Z"})}),tp=s=>e.jsx("svg",{className:"inline-block",viewBox:"0 0 24 24",width:"1.2em",height:"1.2em",...s,children:e.jsx("path",{fill:"currentColor",d:"M21.92 11.6C19.9 6.91 16.1 4 12 4s-7.9 2.91-9.92 7.6a1 1 0 0 0 0 .8C4.1 17.09 7.9 20 12 20s7.9-2.91 9.92-7.6a1 1 0 0 0 0-.8M12 18c-3.17 0-6.17-2.29-7.9-6C5.83 8.29 8.83 6 12 6s6.17 2.29 7.9 6c-1.73 3.71-4.73 6-7.9 6m0-10a4 4 0 1 0 4 4a4 4 0 0 0-4-4m0 6a2 2 0 1 1 2-2a2 2 0 0 1-2 2"})}),ap=s=>{const{t:n}=M("ticket");return[{accessorKey:"id",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.id")}),cell:({row:a})=>e.jsx(K,{variant:"outline",children:a.getValue("id")}),enableSorting:!1,enableHiding:!1},{accessorKey:"subject",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.subject")}),cell:({row:a})=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(sp,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"max-w-[500px] truncate font-medium",children:a.getValue("subject")})]}),enableSorting:!1,enableHiding:!1,size:4e3},{accessorKey:"level",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.level")}),cell:({row:a})=>{const r=a.getValue("level"),l=r===Me.LOW?"default":r===Me.MIDDLE?"secondary":"destructive";return e.jsx(K,{variant:l,className:"whitespace-nowrap",children:n(`level.${r===Me.LOW?"low":r===Me.MIDDLE?"medium":"high"}`)})},filterFn:(a,r,l)=>l.includes(a.getValue(r))},{accessorKey:"status",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.status")}),cell:({row:a})=>{const r=a.getValue("status"),l=a.original.reply_status,c=r===Is.CLOSED?n("status.closed"):n(l===0?"status.replied":"status.pending"),o=r===Is.CLOSED?"default":l===0?"secondary":"destructive";return e.jsx(K,{variant:o,className:"whitespace-nowrap",children:c})}},{accessorKey:"updated_at",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.updated_at")}),cell:({row:a})=>e.jsxs("div",{className:"flex items-center space-x-2 text-muted-foreground",children:[e.jsx(vl,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:_e(a.getValue("updated_at"))})]}),enableSorting:!0},{accessorKey:"created_at",header:({column:a})=>e.jsx(L,{column:a,title:n("columns.created_at")}),cell:({row:a})=>e.jsx("div",{className:"text-sm text-muted-foreground",children:_e(a.getValue("created_at"))}),enableSorting:!0,meta:{isFlexGrow:!0}},{id:"actions",header:({column:a})=>e.jsx(L,{className:"justify-end",column:a,title:n("columns.actions")}),cell:({row:a})=>{const r=a.original.status!==Is.CLOSED;return e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx(ep,{ticketId:a.original.id,dialogTrigger:e.jsx(X,{variant:"ghost",size:"icon",className:"h-8 w-8",title:n("actions.view_details"),children:e.jsx(tp,{className:"h-4 w-4"})})}),r&&e.jsx(We,{title:n("actions.close_confirm_title"),description:n("actions.close_confirm_description"),confirmText:n("actions.close_confirm_button"),variant:"destructive",onConfirm:async()=>{gd(a.original.id).then(()=>{A.success(n("actions.close_success")),s()})},children:e.jsx(X,{variant:"ghost",size:"icon",className:"h-8 w-8",title:n("actions.close_ticket"),children:e.jsx(jl,{className:"h-4 w-4"})})})]})}}]};function np(){const[s,n]=m.useState({}),[a,r]=m.useState({}),[l,c]=m.useState([{id:"status",value:"0"}]),[o,u]=m.useState([]),[x,i]=m.useState({pageIndex:0,pageSize:20}),{refetch:d,data:f,isLoading:w}=ie({queryKey:["orderList",x,l,o],queryFn:()=>pd({pageSize:x.pageSize,current:x.pageIndex+1,filter:l,sort:o})}),P=ts({data:f?.data??[],columns:ap(d),state:{sorting:o,columnVisibility:a,rowSelection:s,columnFilters:l,pagination:x},rowCount:f?.total??0,manualPagination:!0,manualFiltering:!0,manualSorting:!0,enableRowSelection:!0,onRowSelectionChange:n,onSortingChange:u,onColumnFiltersChange:c,onColumnVisibilityChange:r,getCoreRowModel:as(),getFilteredRowModel:cs(),getPaginationRowModel:ds(),onPaginationChange:i,getSortedRowModel:us(),getFacetedRowModel:Ts(),getFacetedUniqueValues:Ds(),initialState:{columnPinning:{right:["actions"]}}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(Hh,{table:P,refetch:d}),e.jsx(xs,{table:P,showPagination:!0})]})}function rp(){const{t:s}=M("ticket");return e.jsxs(Pe,{children:[e.jsxs(Ee,{children:[e.jsx(qe,{}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ze,{}),e.jsx(Le,{})]})]}),e.jsxs(Ve,{className:"flex flex-col",fixedHeight:!0,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:s("title")}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:s("description")})]})}),e.jsx("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0",children:e.jsx(np,{})})]})]})}const lp=Object.freeze(Object.defineProperty({__proto__:null,default:rp},Symbol.toStringTag,{value:"Module"}));export{up as a,cp as c,dp as g,mp as r};
