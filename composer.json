{"name": "xboard/xboard", "type": "project", "description": "xboard is a proxy protocol manage.", "keywords": ["xboard", "v2ray", "shadowsocks", "trojan", "laravel"], "license": "MIT", "require": {"php": "^8.2", "bacon/bacon-qr-code": "^2.0", "doctrine/dbal": "^3.7", "google/cloud-storage": "^1.35", "google/recaptcha": "^1.2", "guzzlehttp/guzzle": "^7.8", "laravel/framework": "^11.0", "laravel/horizon": "^5.21", "laravel/octane": "^2.3", "laravel/prompts": "^0.1.13", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "linfo/linfo": "^4.0", "paragonie/sodium_compat": "^1.20", "php-curl-class/php-curl-class": "^8.6", "spatie/db-dumper": "^3.4", "stripe/stripe-php": "^7.36.1", "symfony/http-client": "^6.4", "symfony/mailgun-mailer": "^6.4", "symfony/yaml": "*", "tormjens/eventy": "^0.8.0", "zoujingli/ip2region": "^2.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.9", "fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "orangehill/iseed": "^3.0", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.4"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Library\\": "library/", "Plugin\\": "plugins/"}, "classmap": ["database/seeders", "database/factories"], "files": ["app/Helpers/Functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "repositories": [{"type": "composer", "url": "https://packagist.org"}]}